package com.hzw.heterogeneous.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.response.EntityTypeNumVo;
import com.hzw.heterogeneous.controller.response.PersonVo;
import com.hzw.heterogeneous.model.Person;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PersonMapper extends BaseMapper<Person> {
    /**
     * 获取各个类型任务数量
     * @return
     */
    List<EntityTypeNumVo> listPersonNum();

    IPage<PersonVo> listPersonPage(@Param("page")IPage<PersonVo> paging, @Param("condition")ThematicAnalysisCondition condition);

    PersonVo getPersonInfo(@Param("id")String id);
    /**
     * 根据图片路径列表批量查询人员信息
     * @param paths 图片路径列表
     * @return 人员信息列表
     */
    List<Person> selectByPaths(List<String> paths);
    
    /**
     * 根据关键词模糊查询人物
     * @param keyword 搜索关键词
     * @param limit 结果数量限制
     * @return 人物列表
     */
    List<Person> searchPersonsByKeyword(@Param("keyword") String keyword, @Param("limit") Integer limit);
}
