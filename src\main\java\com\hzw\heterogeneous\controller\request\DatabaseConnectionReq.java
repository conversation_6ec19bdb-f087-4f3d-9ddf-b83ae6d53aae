package com.hzw.heterogeneous.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 数据库连接配置请求
 * <AUTHOR>
 * @date 2025-08-31
 */
@ApiModel("数据库连接配置请求")
@Data
public class DatabaseConnectionReq {

    @ApiModelProperty(value = "数据库类型：1-MySQL, 2-Oracle, 3-MongoDB", required = true)
    @NotNull(message = "数据库类型不能为空")
    private Integer dbType;

    @ApiModelProperty(value = "数据库地址", required = true)
    @NotBlank(message = "数据库地址不能为空")
    private String host;

    @ApiModelProperty(value = "数据库端口", required = true)
    @NotNull(message = "数据库端口不能为空")
    private Integer port;

    @ApiModelProperty(value = "数据库名称/MongoDB数据库名", required = true)
    @NotBlank(message = "数据库名称不能为空")
    private String database;

    @ApiModelProperty(value = "用户名", required = true)
    @NotBlank(message = "用户名不能为空")
    private String username;

    @ApiModelProperty(value = "密码", required = true)
    @NotBlank(message = "密码不能为空")
    private String password;

    @ApiModelProperty(value = "连接参数，如charset=utf8mb4&useSSL=false")
    private String parameters;

    @ApiModelProperty(value = "连接超时时间（秒），默认30秒")
    private Integer connectTimeout = 30;

    @ApiModelProperty(value = "读取超时时间（秒），默认60秒")
    private Integer readTimeout = 60;

    /**
     * 获取JDBC连接URL
     */
    public String getJdbcUrl() {
        switch (dbType) {
            case 1: // MySQL
                String mysqlUrl = "jdbc:mysql://" + host + ":" + port + "/" + database;
                if (parameters != null && !parameters.trim().isEmpty()) {
                    mysqlUrl += "?" + parameters;
                } else {
                    mysqlUrl += "?useSSL=false&serverTimezone=Asia/Shanghai&characterEncoding=utf8";
                }
                return mysqlUrl;
            case 2: // Oracle
                return "jdbc:oracle:thin:@" + host + ":" + port + ":" + database;
            default:
                throw new IllegalArgumentException("不支持的数据库类型: " + dbType);
        }
    }

    /**
     * 获取数据库驱动类名
     */
    public String getDriverClassName() {
        switch (dbType) {
            case 1: // MySQL
                return "com.mysql.cj.jdbc.Driver";
            case 2: // Oracle
                return "oracle.jdbc.driver.OracleDriver";
            default:
                throw new IllegalArgumentException("不支持的数据库类型: " + dbType);
        }
    }
}