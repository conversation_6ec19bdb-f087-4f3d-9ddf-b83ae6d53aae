package com.hzw.heterogeneous.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 综合关键词搜索请求对象
 */
@Data
@ApiModel("综合关键词搜索请求")
public class ComprehensiveSearchReq {
    
    @ApiModelProperty("搜索关键词")
    private String keyWords;
    
    @ApiModelProperty("页面类型")
    private Integer pageType;
    
    @ApiModelProperty("返回结果数量限制，默认10")
    private Integer limit = 10;
}