package com.hzw.heterogeneous.controller.response;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * HotTopicsVo响应类 - 热点话题数据传输对象
 * 对应实体类: HotTopics (t_hot_topics表)
 * Created by pj on 2024/11/12
 */
@ApiModel(description = "热点话题响应对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HotTopicsVo implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @Excel(name = "标题", orderNum = "0", width = 25)
    @ApiModelProperty(value = "标题")
    private String title;

    @Excel(name = "时间", orderNum = "1", format = "yyyy-MM-dd HH:mm:ss", width = 20)
    @ApiModelProperty(value = "时间")
    private Date time;

    @Excel(name = "地点", orderNum = "2", width = 20)
    @ApiModelProperty(value = "地点")
    private String location;

    @Excel(name = "类型", orderNum = "3", width = 15)
    @ApiModelProperty(value = "类型")
    private String type;

    @Excel(name = "内容", orderNum = "4", width = 30)
    @ApiModelProperty(value = "内容")
    private String content;

    @Excel(name = "标签", orderNum = "5", width = 20)
    @ApiModelProperty(value = "标签")
    private String tags;

    @Excel(name = "来源", orderNum = "6", width = 20)
    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;

    @Excel(name = "关键词标签", orderNum = "7", width = 25)
    @ApiModelProperty(value = "关键词标签")
    private String keyTags;

    private static final long serialVersionUID = 1L;
}