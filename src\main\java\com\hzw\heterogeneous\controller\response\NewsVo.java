package com.hzw.heterogeneous.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * NewsVo - 新闻查询返回对象
 * <AUTHOR>
 * @date 2024/11/12
 */
@ApiModel("新闻信息VO")
@Data
public class NewsVo implements Serializable {

    @ApiModelProperty(value = "新闻ID")
    private String id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "标签")
    private String tags;

    @ApiModelProperty(value = "关键词标签")
    private String keyTags;

    @ApiModelProperty(value = "正文内容")
    private String content;

    @ApiModelProperty(value = "发布时间")
    private Date time;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;

    @ApiModelProperty(value = "事件抽取状态")
    private Integer eventExtract;

    private static final long serialVersionUID = 1L;
}