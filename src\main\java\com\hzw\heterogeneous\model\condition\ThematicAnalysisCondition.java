package com.hzw.heterogeneous.model.condition;

import com.hzw.heterogeneous.util.BaseCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ThematicAnalysisCondition extends BaseCondition {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "主题事件分类")
    private String topicEvent;

    @ApiModelProperty(value = "关键词")
    private String keyWords;

    @ApiModelProperty(value = "主题事件分类")
    private String words;

    @ApiModelProperty(value = "排序方式  1 相关度  2 时间")
    private Integer orderType;

    @ApiModelProperty(value = "排序方式  1 升序  2 降序")
    private Integer orderRule;

    @ApiModelProperty(value = "导出数据id")
    private List<String> ids;

    /**
     * 类型
     */
    private String type;
}
