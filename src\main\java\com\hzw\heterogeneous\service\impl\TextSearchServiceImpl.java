package com.hzw.heterogeneous.service.impl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.constant.TextTypeEnum;
import com.hzw.heterogeneous.controller.request.EntitySearchReq;
import com.hzw.heterogeneous.controller.request.TextSearchCondition;
import com.hzw.heterogeneous.controller.response.ComprehensiveSearchVo;
import com.hzw.heterogeneous.controller.response.EntitySearchVo;
import com.hzw.heterogeneous.controller.response.RecommendationVo;
import com.hzw.heterogeneous.controller.response.TextSearchVo;
import com.hzw.heterogeneous.mapper.PersonMapper;
import com.hzw.heterogeneous.mapper.TextSearchMapper;
import com.hzw.heterogeneous.mapper.WeaponMapper;
import com.hzw.heterogeneous.model.Person;
import com.hzw.heterogeneous.model.Weapon;
import com.hzw.heterogeneous.service.PictureSearchService;
import com.hzw.heterogeneous.service.TextSearchService;
import com.hzw.heterogeneous.util.AIServiceClient;
import com.hzw.heterogeneous.util.ExtendedPage;
import com.hzw.heterogeneous.util.NlpUtils;
import com.hzw.heterogeneous.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2024-05-14
 */
@Slf4j
@Service
public class TextSearchServiceImpl implements TextSearchService {

    @Autowired
    private TextSearchMapper textSearchMapper;

    @Autowired
    private PictureSearchService pictureSearchService;
    
    @Autowired
    private PersonMapper personMapper;
    
    @Autowired
    private WeaponMapper weaponMapper;

    @Value("${api.ai.url:http://127.0.0.1:8001}")
    private String aiApiUrl;


    @Override
    public IPage<TextSearchVo> listPage(TextSearchCondition condition) {
        // 参数验证
        if (condition == null) {
            throw new IllegalArgumentException("搜索条件不能为空");
        }
        if (condition.getTextType() == null) {
            throw new IllegalArgumentException("文本类型不能为空");
        }
        
        log.info("开始文本搜索，搜索类型: {}, 原始关键词: {}, 意图句子: {}", 
                condition.getTextType(), condition.getKeyWords(), condition.getKeySentence());
        
        try {
            AIServiceClient client = new AIServiceClient(aiApiUrl);
            List<String> allKeywords = new ArrayList<>();
            
            // 处理普通关键词 - 多语言支持
            allKeywords.addAll(processRegularKeywords(client, condition.getKeyWords()));
            
            // 处理意图搜索 - 新增多语言支持
            allKeywords.addAll(processIntentSearch(client, condition.getKeySentence()));
            
            // 处理高级搜索关键词
            allKeywords.addAll(processAdvancedKeywords(client, condition));
            
            // 构建搜索参数
            String keyWordsParam = buildSearchParam(allKeywords);
            condition.setKeyWordsParam(keyWordsParam);
            
            log.info("关键词处理完成，最终搜索参数: {}, 关键词总数: {}", 
                    keyWordsParam, allKeywords.size());
            
            // 执行分页查询
            IPage<TextSearchVo> pageResult = executeSearch(condition);
            
            log.info("文本搜索完成，结果数量: {}, 总记录数: {}", 
                    pageResult.getRecords().size(), pageResult.getTotal());
            
            // 返回带有keyWordsParam的扩展分页对象
            return ExtendedPage.of(pageResult, keyWordsParam);
            
        } catch (IOException e) {
            log.error("AI服务调用异常，搜索类型: {}, 错误信息: {}", condition.getTextType(), e.getMessage(), e);
            throw new RuntimeException("AI服务调用失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("文本搜索过程中发生未知异常，搜索类型: {}", condition.getTextType(), e);
            throw new RuntimeException("文本搜索失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 处理普通关键词，支持多语言翻译
     * 
     * @param client AI服务客户端
     * @param keyWords 关键词字符串
     * @return 处理后的关键词列表
     * @throws IOException AI服务异常
     */
    private List<String> processRegularKeywords(AIServiceClient client, String keyWords) throws IOException {
        List<String> result = new ArrayList<>();
        
        if (StringUtils.isBlank(keyWords)) {
            return result;
        }
        
        log.debug("开始处理普通关键词: {}", keyWords);
        String[] keywords = keyWords.split("&");
        
        for (String keyword : keywords) {
            if (StringUtils.isNotBlank(keyword.trim())) {
                result.addAll(translateKeyword(client, keyword.trim()));
            }
        }
        
        log.debug("普通关键词处理完成，原始: {}, 处理后数量: {}", keyWords, result.size());
        return result;
    }
    
    /**
     * 处理意图搜索，新增多语言支持
     * 
     * @param client AI服务客户端
     * @param keySentence 意图句子
     * @return 处理后的关键词列表
     * @throws IOException AI服务异常
     */
    private List<String> processIntentSearch(AIServiceClient client, String keySentence) throws IOException {
        List<String> result = new ArrayList<>();
        
        if (StringUtils.isBlank(keySentence)) {
            return result;
        }
        
        log.debug("开始处理意图搜索: {}", keySentence);
        
        // 先进行语种识别
        Map<String, Object> langResult = client.detectLanguage(keySentence);
        String detectedLang = langResult.get("language").toString();
        
        log.debug("意图句子语种识别结果: {}", detectedLang);
        
        List<String> extractedWords = new ArrayList<>();
        
        if ("zh".equals(detectedLang)) {
            // 中文：直接分词
            extractedWords = NlpUtils.extractWordFromText(keySentence);
            log.debug("中文分词结果: {}", extractedWords);
        } else {
            // 非中文：先翻译成中文，再分词
            Map<String, Object> transResult = client.translateText(keySentence, detectedLang, "zh");
            String chineseText = transResult.get("translation").toString();
            log.debug("意图句子翻译为中文: {}", chineseText);
            
            extractedWords = NlpUtils.extractWordFromText(chineseText);
            log.debug("翻译后分词结果: {}", extractedWords);
            
            // 同时保留原始句子用于搜索
            result.add(keySentence);
            
            // 如果分词结果为空或者翻译后的词汇不在分词结果中，直接添加翻译结果
            if (StringUtils.isNotBlank(chineseText) && !chineseText.equals(keySentence)) {
                if (extractedWords.isEmpty() || !extractedWords.contains(chineseText)) {
                    extractedWords.add(chineseText);
                    log.debug("添加翻译词汇到分词结果: {}", chineseText);
                }
            }
        }
        
        // 处理分词结果
        if (extractedWords.size() >= 3) {
            result.addAll(extractedWords.subList(0, 3));
        } else if (!extractedWords.isEmpty()) {
            result.addAll(extractedWords);
        } else {
            // 分词失败时保留原始句子
            result.add(keySentence);
        }
        
        // 为分词结果添加多语言翻译支持
        List<String> translatedWords = new ArrayList<>();
        for (String word : extractedWords) {
            if (StringUtils.isNotBlank(word)) {
                translatedWords.addAll(translateKeyword(client, word));
            }
        }
        result.addAll(translatedWords);
        
        log.debug("意图搜索处理完成，原始: {}, 处理后数量: {}", keySentence, result.size());
        return result;
    }
    
    /**
     * 处理高级搜索关键词
     * 
     * @param client AI服务客户端
     * @param condition 搜索条件
     * @return 处理后的关键词列表
     * @throws IOException AI服务异常
     */
    private List<String> processAdvancedKeywords(AIServiceClient client, TextSearchCondition condition) throws IOException {
        List<String> result = new ArrayList<>();
        
        // 处理高级关键词1-3
        String[] advancedKeywords = {
            condition.getKeyWords1(),
            condition.getKeyWords2(), 
            condition.getKeyWords3()
        };
        
        for (int i = 0; i < advancedKeywords.length; i++) {
            String keyword = advancedKeywords[i];
            if (StringUtils.isNotBlank(keyword)) {
                log.debug("处理高级关键词{}: {}", (i + 1), keyword);
                result.addAll(translateKeyword(client, keyword.trim()));
            }
        }
        
        log.debug("高级关键词处理完成，处理后数量: {}", result.size());
        return result;
    }
    
    /**
     * 翻译单个关键词，支持中英文互译
     * 
     * @param client AI服务客户端
     * @param keyword 关键词
     * @return 翻译后的关键词列表（包含原词和译词）
     * @throws IOException AI服务异常
     */
    private List<String> translateKeyword(AIServiceClient client, String keyword) throws IOException {
        List<String> result = new ArrayList<>();
        
        // 保留原词
        result.add(keyword);
        
        try {
            // 语种识别
            Map<String, Object> langResult = client.detectLanguage(keyword);
            String lang = langResult.get("language").toString();
            
            log.debug("关键词: {}", keyword);
            Map<String, Object> transResult;
            if (!"zh".equals(lang)) {
                // 非中文 → 中文
                transResult = client.translateText(keyword, lang, "zh");
            } else {
                // 中文 → 英文
                transResult = client.translateText(keyword, lang, "en");
            }
            
            String translation = transResult.get("translation").toString();
            
            // 清理翻译结果，移除AI服务返回的指令文本
            String cleanedTranslation = cleanTranslationResult(translation);
            
            if (StringUtils.isNotBlank(cleanedTranslation) && !cleanedTranslation.equals(keyword)) {
                result.add(cleanedTranslation); // 添加译词
                log.debug("关键词翻译成功: {} -> {}", keyword, cleanedTranslation);
            } else {
                log.debug("翻译结果无效或与原词相同: {} -> {}", keyword, cleanedTranslation);
            }
        } catch (Exception e) {
            log.warn("关键词翻译失败，将使用原始关键词: {}, 错误: {}", keyword, e.getMessage());
        }
        
        return result; // 返回 [原词, 译词]
    }
    
    /**
     * 清理翻译结果，移除AI服务返回的指令文本和污染内容
     * 
     * @param rawTranslation 原始翻译结果
     * @return 清理后的翻译结果
     */
    private String cleanTranslationResult(String rawTranslation) {
        if (StringUtils.isBlank(rawTranslation)) {
            return "";
        }
        
        log.debug("清理翻译结果: {}", rawTranslation);
        
        // 移除AI服务返回的常见指令文本和污染内容
        String cleaned = rawTranslation
                .replaceAll("\\s*\\n+\\s*Please return the translation result only.*", "")  // 移除指令文本
                .replaceAll("\\s*without any.*?tags.*", "")  // 移除说明文本
                .replaceAll("\\s*intermediate analysis.*", "")  // 移除分析文本  
                .replaceAll("\\s*Just respond.*", "")  // 移除回复指令
                .replaceAll("<[^>]*>", "")  // 移除HTML标签
                .replaceAll("\\n+", " ")  // 移除换行符
                .replaceAll("\\s+", " ")  // 合并多余空格
                .trim();
                
        log.debug("清理后的翻译结果: {}", cleaned);
        return cleaned;
    }
    
    /**
     * 构建搜索参数字符串
     * 
     * @param keywords 关键词列表
     * @return 格式化的搜索参数
     */
    private String buildSearchParam(List<String> keywords) {
        if (keywords.isEmpty()) {
            return "";
        }
        
        // 去重并过滤空字符串
        Set<String> uniqueKeywords = keywords.stream()
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toSet());
        
        if (uniqueKeywords.isEmpty()) {
            return "";
        }
        
        // 构建 "+ 关键词1 + 关键词2" 格式
        return uniqueKeywords.stream()
                .collect(Collectors.joining(" + ", " + ", ""));
    }
    
    /**
     * 执行搜索查询
     * 
     * @param condition 搜索条件
     * @return 分页查询结果
     */
    private IPage<TextSearchVo> executeSearch(TextSearchCondition condition) {
        try {
            if (TextTypeEnum.NEWS.getCode().equals(condition.getTextType())) {
                log.debug("执行新闻搜索查询");
                return textSearchMapper.newsListPage(condition.buildPage(), condition);
            } else if (TextTypeEnum.LITERATURE.getCode().equals(condition.getTextType())) {
                log.debug("执行文献搜索查询");
                return textSearchMapper.literatureListPage(condition.buildPage(), condition);
            } else {
                throw new IllegalArgumentException("不支持的文本类型: " + condition.getTextType());
            }
        } catch (Exception e) {
            log.error("数据库查询执行失败，搜索类型: {}", condition.getTextType(), e);
            throw new RuntimeException("数据库查询失败: " + e.getMessage(), e);
        }
    }

//    public static void main(String[] args) {
//        List<String> keySentenceWords = new ArrayList<>();
//        keySentenceWords.add("中国");
//        keySentenceWords.add("北京");
//        keySentenceWords.add("上海");
//
//        // 拼接字符串 '+中国 +北京 +上海'
//        StringBuilder keyWordsParam = new StringBuilder();
//        keyWordsParam = new StringBuilder("+" + keySentenceWords.get(0));
//        for (int i = 1; i < keySentenceWords.size(); i++) {
//            keyWordsParam.append(" +").append(keySentenceWords.get(i));
//        }
////        keySentenceWords.forEach(item -> {
////            keyWordsParam += "+" + item;
////        });
////        String keyWordsParam = "";//StringUtils.join("+"+keySentenceWords, " ");
////        keySentenceWords.forEach(item -> {
////            keyWordsParam += "+" + item;
////        });
//        System.out.println(keyWordsParam);
//    }
    @Override
    public Result<List<EntitySearchVo>> queryEntityByTags(EntitySearchReq req) {
        String tags = StringUtils.join(req.getTagList(), " ");
        List<EntitySearchVo> resultList = textSearchMapper.queryEntityByTags(tags);
        for(EntitySearchVo entitySearchVo:resultList){
            String base64 = pictureSearchService.getBase64(entitySearchVo.getUrl());
            entitySearchVo.setBase64(base64);
        }
        return Result.ok(resultList);
    }

    @Override
    public TextSearchVo queryEntityById(TextSearchCondition condition) {
        if(TextTypeEnum.NEWS.getCode().equals(condition.getTextType())){
            return textSearchMapper.getNewsById(condition.getId());
        }else{
            return textSearchMapper.getLiteratureById(condition.getId());
        }
    }
    
    @Override
    public RecommendationVo getRecommendationList(String keyword, Integer limit) {
        log.info("开始获取推荐列表，关键词: {}, 限制数量: {}", keyword, limit);
        
        // 参数验证
        if (StringUtils.isBlank(keyword)) {
            log.warn("搜索关键词为空，返回空结果");
            RecommendationVo emptyResult = new RecommendationVo();
            emptyResult.setUsers(new ArrayList<>());
            emptyResult.setWepons(new ArrayList<>());
            return emptyResult;
        }
        
        // 设置默认限制数量
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        
        try {
            RecommendationVo result = new RecommendationVo();
            
            // 搜索人物实体（只对user_name/user_name_eng字段进行模糊匹配）
            log.debug("开始搜索人物实体");
            List<Person> persons = personMapper.searchPersonsByKeyword(keyword, limit);
            List<RecommendationVo.PersonItem> userItems = new ArrayList<>();
            for (Person person : persons) {
                userItems.add(new RecommendationVo.PersonItem(
                    person.getId(),
                    person.getUserName(),
                    person.getUserNameEng()
                ));
            }
            result.setUsers(userItems);
            log.debug("人物实体搜索完成，找到 {} 条结果", persons.size());
            
            // 搜索武器实体（只对name/name_eng字段进行模糊匹配）
            log.debug("开始搜索武器实体");
            List<Weapon> weapons = weaponMapper.searchWeaponsByKeyword(keyword, limit);
            List<RecommendationVo.WeaponItem> weaponItems = new ArrayList<>();
            for (Weapon weapon : weapons) {
                weaponItems.add(new RecommendationVo.WeaponItem(
                    weapon.getId(),
                    weapon.getName(),
                    weapon.getNameEng()
                ));
            }
            result.setWepons(weaponItems);
            log.debug("武器实体搜索完成，找到 {} 条结果", weapons.size());
            
            log.info("推荐列表获取完成，关键词: {}, 人物数: {}, 武器数: {}", 
                    keyword, userItems.size(), weaponItems.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取推荐列表过程中发生异常，关键词: {}", keyword, e);
            throw new RuntimeException("获取推荐列表失败: " + e.getMessage(), e);
        }
    }
}
