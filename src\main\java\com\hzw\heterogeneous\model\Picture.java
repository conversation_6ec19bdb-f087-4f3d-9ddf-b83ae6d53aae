package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_picture")
public class Picture {

    /**
     * id
     */
    private String id;
    
    /**
     * 图片名称
     */
    private String name;
    
    /**
     * 图片类型
     */
    private Integer type;
    
    /**
     * 标签
     */
    private String tags;
    
    /**
     * 图片URL路径
     */
    private String url;
    
    /**
     * 创建时间
     */
    private Date createdTime;
    
    /**
     * 更新时间
     */
    private Date updatedTime;
}