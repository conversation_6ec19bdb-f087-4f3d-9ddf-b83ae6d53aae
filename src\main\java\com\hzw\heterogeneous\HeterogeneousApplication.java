package com.hzw.heterogeneous;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@MapperScan(basePackages = "com.hzw.heterogeneous.mapper")
@Slf4j
@EnableAsync
@EnableScheduling
public class HeterogeneousApplication {

    public static void main(String[] args) {
        SpringApplication.run(HeterogeneousApplication.class, args);
    }

}
