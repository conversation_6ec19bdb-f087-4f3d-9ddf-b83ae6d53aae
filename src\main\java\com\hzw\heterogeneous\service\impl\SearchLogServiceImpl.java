package com.hzw.heterogeneous.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.hzw.heterogeneous.controller.response.ComprehensiveSearchVo;
import com.hzw.heterogeneous.mapper.PersonMapper;
import com.hzw.heterogeneous.mapper.SearchLogMapper;
import com.hzw.heterogeneous.mapper.WeaponMapper;
import com.hzw.heterogeneous.model.Person;
import com.hzw.heterogeneous.model.SearchLog;
import com.hzw.heterogeneous.model.Weapon;
import com.hzw.heterogeneous.service.SearchLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SearchLogServiceImpl implements SearchLogService {

    @Autowired
    private SearchLogMapper searchLogMapper;
    
    @Autowired
    private PersonMapper personMapper;
    
    @Autowired
    private WeaponMapper weaponMapper;

    /**
     * 保存搜索日志
     *
     * @param keyWords 搜索关键词，用于记录用户搜索的内容
     * @param pageType 页面类型，用于区分搜索结果页面的类型
     * @return 返回一个布尔值，表示日志是否成功保存
     */
    @Override
    public Boolean saveSearchLog(SearchLog searchLog) {
        if(StringUtils.isNotBlank(searchLog.getKeyWords())){
            searchLog.setKeyWords(searchLog.getKeyWords().trim());
            searchLog.setCreatedTime(new Date());
            searchLog.setUpdatedTime(new Date());
            return searchLogMapper.insert(searchLog) > 0;
        } else {
            return true;
        }
    }

    /**
     * 根据关键词和页面类型查询搜索日志
     *
     * @param keyWords 搜索关键词，用于模糊匹配日志中的关键词
     * @param pageType 页面类型，用于精确匹配日志的页面类型如果为null，则不考虑页面类型
     * @return 返回最多10条匹配的搜索日志列表
     */
    @Override
    public List<SearchLog> listSearchLog(String keyWords, Integer pageType) {
        List<SearchLog> searchLogs = null;
        // 如果 keyWords内容是 中国 ，则搜索结果为返回：中国 、china
        if (("中国".equals(keyWords))){
            searchLogs=new ArrayList<>();
            SearchLog searchLog =new SearchLog();
            searchLog.setKeyWords("中国");
            searchLogs.add(searchLog);
            return searchLogs;
        }
        if ("china".equals(keyWords)){
            searchLogs=new ArrayList<>();
            SearchLog searchLog =new SearchLog();
            searchLog.setKeyWords("china");
            searchLogs.add(searchLog);
            return searchLogs;
        }
        if (("中国 china".equals(keyWords))){
            searchLogs=new ArrayList<>();
            SearchLog searchLog =new SearchLog();
            searchLog.setKeyWords("中国 china");
            searchLogs.add(searchLog);
            searchLog =new SearchLog();
            searchLog.setKeyWords("中国");
            searchLogs.add(searchLog);
            searchLog =new SearchLog();
            searchLog.setKeyWords("china");
            searchLogs.add(searchLog);
//            searchLog =new SearchLog();
//            searchLog.setKeyWords("china");
//            searchLogs.add(searchLog);
            return searchLogs;
        }

        return searchLogMapper.queryList(keyWords, pageType);
    }
    
    /**
     * 综合搜索，包括搜索日志、人物、武器
     * @param keyword 搜索关键词
     * @param pageType 页面类型
     * @param limit 结果数量限制
     * @return 综合搜索结果
     */
    @Override
    public ComprehensiveSearchVo comprehensiveSearch(String keyword, Integer pageType, Integer limit) {
        log.info("开始执行综合搜索，关键词：{}，页面类型：{}，限制数量：{}", keyword, pageType, limit);
        
        // 参数验证
        if (StringUtils.isBlank(keyword)) {
            log.warn("搜索关键词为空，返回空结果");
            return createEmptyResult();
        }
        
        if (limit == null || limit <= 0) {
            limit = 10; // 默认值
        }
        
        ComprehensiveSearchVo result = new ComprehensiveSearchVo();
        List<ComprehensiveSearchVo.SearchResultItem> resultItems = new ArrayList<>();
        
        try {
            // 1. 搜索日志记录 (type = 1)
            List<SearchLog> searchLogs = searchLogMapper.queryList(keyword, pageType);
            if (searchLogs != null) {
                int logCount = 0;
                for (SearchLog log : searchLogs) {
                    if (logCount >= limit) break;
                    if (StringUtils.isNotBlank(log.getKeyWords())) {
                        resultItems.add(new ComprehensiveSearchVo.SearchResultItem(log.getKeyWords(), 1));
                        logCount++;
                    }
                }
                log.debug("搜索日志结果数量：{}", logCount);
            }
            
            // 2. 搜索武器 (type = 2)
            List<Weapon> weapons = weaponMapper.searchWeaponsByKeyword(keyword, limit);
            if (weapons != null) {
                for (Weapon weapon : weapons) {
                    if (StringUtils.isNotBlank(weapon.getName())) {
                        resultItems.add(new ComprehensiveSearchVo.SearchResultItem(weapon.getName(), 2));
                        resultItems.add(new ComprehensiveSearchVo.SearchResultItem(weapon.getNameEng(), 2));
                    }
                }
                log.debug("武器搜索结果数量：{}", weapons.size());
            }
            
            // 3. 搜索人物 (type = 3)
            List<Person> persons = personMapper.searchPersonsByKeyword(keyword, limit);
            if (persons != null) {
                for (Person person : persons) {
                    if (StringUtils.isNotBlank(person.getUserName())) {
                        resultItems.add(new ComprehensiveSearchVo.SearchResultItem(person.getUserName(), 3));
                        resultItems.add(new ComprehensiveSearchVo.SearchResultItem(person.getUserNameEng(), 3));
                    }
                }
                log.debug("人物搜索结果数量：{}", persons.size());
            }
            
            result.setData(resultItems);
            
            log.info("综合搜索完成，总结果数：{}条，其中日志{}条，武器{}条，人物{}条", 
                    resultItems.size(),
                    searchLogs != null ? Math.min(searchLogs.size(), limit) : 0,
                    weapons != null ? weapons.size() : 0,
                    persons != null ? persons.size() : 0);
            
        } catch (Exception e) {
            log.error("综合搜索执行异常，关键词：{}", keyword, e);
            result = createEmptyResult();
        }
        
        return result;
    }
    
    /**
     * 创建空的搜索结果
     * @return 空结果对象
     */
    private ComprehensiveSearchVo createEmptyResult() {
        ComprehensiveSearchVo result = new ComprehensiveSearchVo();
        result.setData(new ArrayList<>());
        return result;
    }
}