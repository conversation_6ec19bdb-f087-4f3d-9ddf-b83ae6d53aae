<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.heterogeneous.mapper.HotTopicsMapper">

    <select id="listPage" resultType="com.hzw.heterogeneous.controller.response.HotNewsVo">
        SELECT
            t1.*,
            <!-- 使用MySQL全文索引，提供更好的搜索性能 -->
            (0
            <if test="condition.keyWords != null and condition.keyWords != ''">
                + MATCH(t1.title, t1.content, t1.tags, t1.key_tags) AGAINST (#{condition.keyWords} IN BOOLEAN MODE)
            </if>
            ) AS total_relevance
        FROM t_hot_topics t1
        WHERE 1=1
        <if test="condition.keyWords != null and condition.keyWords != ''">
            AND MATCH(t1.title, t1.content, t1.tags, t1.key_tags) AGAINST (#{condition.keyWords} IN BOOLEAN MODE)
        </if>
        <if test="null != condition.type and condition.type != ''">
            AND t1.type = #{condition.type}
        </if>
        <if test="null == condition.orderType or condition.orderType == 1">
            ORDER BY total_relevance
            <if test="null == condition.orderRule or condition.orderRule == 1">
                DESC
            </if>
            <if test="null != condition.orderRule and condition.orderRule == 2">
                ASC
            </if>
        </if>
        <if test="null != condition.orderType and condition.orderType == 2">
            ORDER BY t1.time
            <if test="null == condition.orderRule or condition.orderRule == 1">
                DESC
            </if>
            <if test="null != condition.orderRule and condition.orderRule == 2">
                ASC
            </if>
        </if>
    </select>

    <!-- 根据热点话题ID查询关联的新闻列表，按时间排序 -->
    <select id="listRelatedNewsByHotTopicsId" resultType="com.hzw.heterogeneous.controller.response.NewsVo">
        SELECT 
            n.id,
            n.title,
            n.source,
            n.tags,
            n.key_tags AS keyTags,
            n.content,
            n.time,
            n.created_time AS createdTime,
            n.updated_time AS updatedTime,
            n.event_extract AS eventExtract
        FROM t_news n
        INNER JOIN t_hot_topics_news_relation r ON n.id = r.news_id
        WHERE r.hot_topics_id = #{hotTopicsId}
        ORDER BY n.time DESC
    </select>

</mapper>