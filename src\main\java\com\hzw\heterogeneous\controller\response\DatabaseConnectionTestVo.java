package com.hzw.heterogeneous.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 数据库连接测试响应
 * <AUTHOR>
 * @date 2025-08-31
 */
@ApiModel("数据库连接测试响应")
@Data
public class DatabaseConnectionTestVo {

    @ApiModelProperty(value = "连接是否成功")
    private Boolean success;

    @ApiModelProperty(value = "连接响应时间（毫秒）")
    private Long responseTime;

    @ApiModelProperty(value = "数据库版本信息")
    private String databaseVersion;

    @ApiModelProperty(value = "可用表列表（最多显示20个）")
    private List<String> availableTables;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty(value = "连接详情信息")
    private String connectionInfo;
}