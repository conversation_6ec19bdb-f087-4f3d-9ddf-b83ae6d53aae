package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* HotTopicsNewsRelation实体类 - 热点话题与新闻关联表
* 对应数据库表: t_hot_topics_news_relation
* Created by pj on 2024/11/12
*/
@ApiModel("t_hot_topics_news_relation表 - 热点话题与新闻关联")
@TableName("t_hot_topics_news_relation")
@Data
public class HotTopicsNewsRelation implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 热点话题ID
     */
    private String hotTopicsId;

    /**
     * 新闻ID
     */
    private String newsId;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    private static final long serialVersionUID = 1L;
}