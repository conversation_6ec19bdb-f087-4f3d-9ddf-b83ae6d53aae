package com.hzw.heterogeneous.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 数据同步响应
 * <AUTHOR>
 * @date 2025-08-31
 */
@ApiModel("数据同步响应")
@Data
public class DataSyncVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "同步任务ID")
    private String taskId;

    @ApiModelProperty(value = "同步状态：1-准备中, 2-同步中, 3-同步成功, 4-同步失败")
    private Integer status;

    @ApiModelProperty(value = "同步状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "同步进度（0-100）")
    private Integer progress;

    @ApiModelProperty(value = "源数据库类型：1-MySQL, 2-Oracle, 3-MongoDB")
    private Integer sourceDbType;

    @ApiModelProperty(value = "源数据库地址")
    private String sourceHost;

    @ApiModelProperty(value = "源表名/集合名")
    private String sourceTable;

    @ApiModelProperty(value = "目标数据库名")
    private String targetDatabase;

    @ApiModelProperty(value = "目标表名")
    private String targetTable;

    @ApiModelProperty(value = "总记录数")
    private Long totalRecords;

    @ApiModelProperty(value = "已同步记录数")
    private Long syncedRecords;

    @ApiModelProperty(value = "失败记录数")
    private Long failedRecords;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "耗时（毫秒）")
    private Long duration;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty(value = "同步描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "未知";
        switch (status) {
            case 1: return "准备中";
            case 2: return "同步中";
            case 3: return "同步成功";
            case 4: return "同步失败";
            default: return "未知状态";
        }
    }

    /**
     * 计算耗时
     */
    public Long getDuration() {
        if (startTime != null && endTime != null) {
            return endTime.getTime() - startTime.getTime();
        }
        return duration;
    }
}