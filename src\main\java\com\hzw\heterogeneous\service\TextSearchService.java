package com.hzw.heterogeneous.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.request.EntitySearchReq;
import com.hzw.heterogeneous.controller.request.TextSearchCondition;
import com.hzw.heterogeneous.controller.response.ComprehensiveSearchVo;
import com.hzw.heterogeneous.controller.response.EntitySearchVo;
import com.hzw.heterogeneous.controller.response.RecommendationVo;
import com.hzw.heterogeneous.controller.response.TextSearchVo;
import com.hzw.heterogeneous.util.Result;

import java.util.List;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2024-11-11
 */
public interface TextSearchService {

    /**
     * 分页查询文本数据
     * @param condition 查询条件
     * @return 分页数据，包含keyWordsParam扩展属性
     */
    IPage<TextSearchVo> listPage(TextSearchCondition condition);

    /**
     * 根据标签查询实体
     * @param req
     * @return
     */
    Result<List<EntitySearchVo>> queryEntityByTags(EntitySearchReq req);

    /**
     * 根据实体Id查询实体详细信息
     * @param condition
     * @return
     */
    TextSearchVo queryEntityById(TextSearchCondition condition);

    /**
     * 根据关键词获取推荐列表
     * 搜索关键词模糊匹配人物实体、武器实体的名称，并形成推荐列表
     * @param keyword 搜索关键词
     * @param limit 结果数量限制，默认10条
     * @return 推荐列表
     */
    RecommendationVo getRecommendationList(String keyword, Integer limit);
}
