package com.hzw.heterogeneous.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.response.EntityTypeNumVo;
import com.hzw.heterogeneous.controller.response.PersonVo;
import com.hzw.heterogeneous.controller.response.TopicEventSearchVo;
import com.hzw.heterogeneous.mapper.PersonMapper;
import com.hzw.heterogeneous.mapper.PersonWeaponPictureRelMapper;
import com.hzw.heterogeneous.mapper.PersonWeaponVideoRelMapper;
import com.hzw.heterogeneous.mapper.PictureMapper;
import com.hzw.heterogeneous.mapper.VideoMapper;
import com.hzw.heterogeneous.mapper.PersonTrendMapper;
import com.hzw.heterogeneous.mapper.TrendEventRelationMapper;
import com.hzw.heterogeneous.mapper.EventsMapper;
import com.hzw.heterogeneous.model.Person;
import com.hzw.heterogeneous.model.PersonWeaponPictureRel;
import com.hzw.heterogeneous.model.PersonWeaponVideoRel;
import com.hzw.heterogeneous.model.Picture;
import com.hzw.heterogeneous.model.TopicEventSearch;
import com.hzw.heterogeneous.model.Video;
import com.hzw.heterogeneous.model.PersonTrend;
import com.hzw.heterogeneous.model.TrendEventRelation;
import com.hzw.heterogeneous.model.Events;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import com.hzw.heterogeneous.service.PersonService;
import com.hzw.heterogeneous.service.PictureSearchService;
import com.hzw.heterogeneous.util.Paging;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class PersonServiceImpl implements PersonService {

    private static final Logger logger = LoggerFactory.getLogger(PersonServiceImpl.class);

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private PictureSearchService pictureSearchService;
    
    @Autowired
    private PersonWeaponPictureRelMapper pictureRelMapper;
    
    @Autowired
    private PersonWeaponVideoRelMapper videoRelMapper;
    
    @Autowired
    private PictureMapper pictureMapper;
    
    @Autowired
    private VideoMapper videoMapper;
    
    @Autowired
    private PersonTrendMapper personTrendMapper;
    
    @Autowired
    private TrendEventRelationMapper trendEventRelationMapper;
    
    @Autowired
    private EventsMapper eventsMapper;
    /**
     * 获取各个类型任务数量
     * @param condition
     * @return
     */
    @Override
    public List<EntityTypeNumVo> listPersonNum(ThematicAnalysisCondition condition) {
        return personMapper.listPersonNum();
    }

    /**
     * 分页获取重要人物数据
     * @param condition
     * @return
     */
    @Override
    public Paging<PersonVo> listPersonPage(ThematicAnalysisCondition condition) {
        IPage<PersonVo> paging = condition.customizeBuildPage();
        paging = personMapper.listPersonPage(paging, condition);
        for (PersonVo personVo : paging.getRecords()) {
            // 加载多张图片
            loadPersonPictures(personVo);
            
            // 加载多个视频
            loadPersonVideos(personVo);
            
            // 兼容原有单张图片显示：如果有图片，设置第一张图片的url和base64
            if (personVo.getPictures() != null && !personVo.getPictures().isEmpty()) {
                Picture firstPicture = personVo.getPictures().get(0);
                personVo.setUrl(firstPicture.getUrl());
                String base64 = pictureSearchService.getBase64(firstPicture.getUrl());
                personVo.setBase64(base64);
            }
        }
        return Paging.buildPaging(paging);
    }

    /**
     * 获取人物信息
     * @param condition
     * @return
     */
    @Override
    public PersonVo getPersonInfo(ThematicAnalysisCondition condition) {
        logger.info("开始获取人物信息，人物ID: {}", condition != null ? condition.getId() : "null");
        
        // 参数验证
        if (condition == null || condition.getId() == null || condition.getId().trim().isEmpty()) {
            logger.error("获取人物信息失败：参数不合法，condition: {}", condition);
            throw new IllegalArgumentException("人物ID不能为空");
        }
        
        try {
            // 获取人物基本信息
            logger.debug("查询人物基本信息，人物ID: {}", condition.getId());
            PersonVo personInfo = personMapper.getPersonInfo(condition.getId());
            if (personInfo == null) {
                logger.warn("未找到指定的人物信息，人物ID: {}", condition.getId());
                return null;
            }
            
            // 处理单张图片的base64编码（如果有的话，会在加载图片时处理）
            // 这里不再单独处理url字段，因为Mapper中已经移除了图片关联查询
            
            // 加载多张图片
            logger.debug("开始加载人物多张图片，人物ID: {}", condition.getId());
            loadPersonPictures(personInfo);
            
            // 加载多个视频
            logger.debug("开始加载人物多个视频，人物ID: {}", condition.getId());
            loadPersonVideos(personInfo);
            
            // 加载人物动向及关联事件
            logger.debug("开始加载人物动向及关联事件，人物ID: {}", condition.getId());
            loadPersonTrendsWithEvents(personInfo);
            
            logger.info("成功获取人物信息，人物ID: {}, 姓名: {}, 动向数量: {}, 图片数量: {}, 视频数量: {}", 
                    condition.getId(), 
                    personInfo.getUserName(),
                    personInfo.getPersonTrends() != null ? personInfo.getPersonTrends().size() : 0,
                    personInfo.getPictures() != null ? personInfo.getPictures().size() : 0,
                    personInfo.getVideos() != null ? personInfo.getVideos().size() : 0);
            
            return personInfo;
        } catch (Exception e) {
            logger.error("获取人物信息异常，人物ID: {}", condition.getId(), e);
            throw new RuntimeException("获取人物信息失败", e);
        }
    }

    @Override
    public List<PersonVo> exportList(List<String> ids) {
        logger.info("开始导出人物数据，导出ID数量: {}", ids != null ? ids.size() : 0);
        
        if (CollectionUtils.isEmpty(ids)) {
            logger.warn("导出人物数据失败：导出ID列表为空");
            return new ArrayList<>();
        }
        
        try {
            // 1. 查询人物基本信息
            LambdaQueryWrapper<Person> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Person::getId, ids);
            List<Person> personList = personMapper.selectList(queryWrapper);
            
            if (CollectionUtils.isEmpty(personList)) {
                logger.warn("未找到对应的人物数据，IDs: {}", ids);
                return new ArrayList<>();
            }
            
            List<PersonVo> personVos = new ArrayList<>();
            
            // 2. 转换为 PersonVo 并加载相关数据（为了提高导出效率，可以选择性加载）
            for (Person person : personList) {
                PersonVo personVo = new PersonVo();
                BeanUtils.copyProperties(person, personVo);
                
                // 对于导出功能，可以选择不加载多媒体和动向数据以提高性能
                // 如果需要完整数据，可以取消以下注释：
                // loadPersonPictures(personVo);
                // loadPersonVideos(personVo);
                // loadPersonTrendsWithEvents(personVo);
                
                personVos.add(personVo);
            }
            
            logger.info("成功导出人物数据，实际导出数量: {}", personVos.size());
            return personVos;
            
        } catch (Exception e) {
            logger.error("导出人物数据失败，IDs: {}", ids, e);
            throw new RuntimeException("导出人物数据失败", e);
        }
    }
    
    /**
     * 加载人物关联的多张图片
     * @param personVo
     */
    private void loadPersonPictures(PersonVo personVo) {
        List<PersonWeaponPictureRel> pictureRels = pictureRelMapper.selectList(
            new LambdaQueryWrapper<PersonWeaponPictureRel>()
                .eq(PersonWeaponPictureRel::getPersonWeaponId, personVo.getId())
        );
        
        if (pictureRels != null && !pictureRels.isEmpty()) {
            List<String> pictureIds = pictureRels.stream()
                .map(PersonWeaponPictureRel::getPictureId)
                .collect(java.util.stream.Collectors.toList());
            
            List<Picture> pictures = pictureMapper.selectBatchIds(pictureIds);
            personVo.setPictures(pictures);
        } else {
            // 如果没有图片，设置为空列表
            personVo.setPictures(new ArrayList<>());
        }
    }
    
    /**
     * 加载人物关联的多个视频
     * @param personVo
     */
    private void loadPersonVideos(PersonVo personVo) {
        List<PersonWeaponVideoRel> videoRels = videoRelMapper.selectList(
            new LambdaQueryWrapper<PersonWeaponVideoRel>()
                .eq(PersonWeaponVideoRel::getPersonWeaponId, personVo.getId())
        );
        
        if (videoRels != null && !videoRels.isEmpty()) {
            List<String> videoIds = videoRels.stream()
                .map(PersonWeaponVideoRel::getVideoId)
                .collect(java.util.stream.Collectors.toList());
            
            List<Video> videos = videoMapper.selectBatchIds(videoIds);
            personVo.setVideos(videos);
            
            // 可以根据需要生成视频封面图的base64列表
            // List<String> thumbnailBase64List = new ArrayList<>();
            // for (Video video : videos) {
            //     // 这里可以扩展视频封面图生成逻辑
            //     thumbnailBase64List.add("");
            // }
            // personVo.setVideoThumbnailBase64List(thumbnailBase64List);
        } else {
            // 如果没有视频，设置为空列表
            personVo.setVideos(new ArrayList<>());
        }
    }
    
    /**
     * 加载人物动向及关联事件
     * @param personVo
     */
    private void loadPersonTrendsWithEvents(PersonVo personVo) {
        logger.debug("开始加载人物动向数据，人物ID: {}", personVo.getId());
        
        try {
            // 1. 先查询人物动向列表，按创建时间倒序排序（最新在前）
            List<PersonTrend> personTrends = personTrendMapper.selectList(
                new LambdaQueryWrapper<PersonTrend>()
                    .eq(PersonTrend::getPersonId, personVo.getId())
                    .orderByDesc(PersonTrend::getCreatedTime)
                    .orderByDesc(PersonTrend::getTime)
            );
            
            if (personTrends != null && !personTrends.isEmpty()) {
                logger.debug("找到{}\u6761人物动向记录，人物ID: {}", personTrends.size(), personVo.getId());
                
                // 2. 为每个动向加载关联的事件
                for (PersonTrend trend : personTrends) {
                    loadTrendRelatedEvents(trend);
                }
                personVo.setPersonTrends(personTrends);
                
                logger.debug("成功加载人物动向数据，人物ID: {}, 动向数量: {}", 
                        personVo.getId(), personTrends.size());
            } else {
                logger.debug("未找到人物动向记录，人物ID: {}", personVo.getId());
                personVo.setPersonTrends(new ArrayList<>());
            }
        } catch (Exception e) {
            logger.error("加载人物动向数据异常，人物ID: {}", personVo.getId(), e);
            personVo.setPersonTrends(new ArrayList<>());
        }
    }
    
    /**
     * 为单个动向加载关联的事件
     * @param trend
     */
    private void loadTrendRelatedEvents(PersonTrend trend) {
        // 1. 查询动向关联的事件ID列表
        List<TrendEventRelation> relations = trendEventRelationMapper.selectList(
            new LambdaQueryWrapper<TrendEventRelation>()
                .eq(TrendEventRelation::getTrendId, trend.getId())
        );
        
        if (relations != null && !relations.isEmpty()) {
            // 2. 获取事件ID列表
            List<String> eventIds = relations.stream()
                .map(TrendEventRelation::getEventId)
                .collect(java.util.stream.Collectors.toList());
            
            // 3. 批量查询事件详情，按时间先后顺序排序
            List<Events> events = eventsMapper.selectBatchIds(eventIds);
            if (events != null && !events.isEmpty()) {
                // 按时间排序（时间正序，先发生的在前）
                events.sort((e1, e2) -> {
                    if (e1.getTime() == null && e2.getTime() == null) {
                        return 0;
                    }
                    if (e1.getTime() == null) {
                        return 1;
                    }
                    if (e2.getTime() == null) {
                        return -1;
                    }
                    return e1.getTime().compareTo(e2.getTime());
                });
            }
            trend.setRelatedEvents(events);
        } else {
            trend.setRelatedEvents(new ArrayList<>());
        }
    }
    
    /**
     * 获取指定动向的关联事件列表
     * @param trendId 动向ID
     * @return 关联的事件列表，按时间先后顺序排序
     */
    @Override
    public List<Events> getTrendRelatedEvents(String trendId) {
        // 参数验证
        if (trendId == null || trendId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        // 1. 查询动向关联的事件ID列表
        List<TrendEventRelation> relations = trendEventRelationMapper.selectList(
            new LambdaQueryWrapper<TrendEventRelation>()
                .eq(TrendEventRelation::getTrendId, trendId)
        );
        
        if (relations == null || relations.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 2. 获取事件ID列表
        List<String> eventIds = relations.stream()
            .map(TrendEventRelation::getEventId)
            .collect(java.util.stream.Collectors.toList());
        
        // 3. 批量查询事件详情，按时间先后顺序排序
        List<Events> events = eventsMapper.selectBatchIds(eventIds);
        if (events == null || events.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 按时间排序（时间正序，先发生的在前）
        events.sort((e1, e2) -> {
            if (e1.getTime() == null && e2.getTime() == null) {
                return 0;
            }
            if (e1.getTime() == null) {
                return 1;
            }
            if (e2.getTime() == null) {
                return -1;
            }
            return e1.getTime().compareTo(e2.getTime());
        });
        
        return events;
    }
}
