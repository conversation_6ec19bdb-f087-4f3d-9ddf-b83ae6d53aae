SET FOREIGN_KEY_CHECKS=0;

CREATE TABLE `multi-source`.`t_data_access`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `file_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件名称',
  `file_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件地址',
  `file_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件类型',
  `file_format` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件格式',
  `data_type` int NULL DEFAULT NULL COMMENT '数据类型 1:数据库 2:文件',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `is_delete` int NULL DEFAULT 0 COMMENT '逻辑删除0正常1删除',
  `remark` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  FULLTEXT INDEX `idx_fulltext_name_type`(`file_name`, `file_type`, `file_format`) WITH PARSER `ngram`
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '数据接入表' ROW_FORMAT = Dynamic;

ALTER TABLE `multi-source`.`t_events` ADD FULLTEXT INDEX `idx_full`(`title`, `content`, `tags`) WITH PARSER `ngram`;

ALTER TABLE `multi-source`.`t_literature` ADD FULLTEXT INDEX `idx_full`(`title`, `summary`) WITH PARSER `ngram`;

ALTER TABLE `multi-source`.`t_news` ADD FULLTEXT INDEX `idx_fulltext_title_content`(`title`, `content`) WITH PARSER `ngram`;

ALTER TABLE `multi-source`.`t_person` ADD FULLTEXT INDEX `idx_full`(`user_name`, `tags`) WITH PARSER `ngram`;

ALTER TABLE `multi-source`.`t_person` ADD FULLTEXT INDEX `idx_tags`(`tags`) WITH PARSER `ngram`;

ALTER TABLE `multi-source`.`t_picture` ADD FULLTEXT INDEX `idx_full`(`name`, `tags`) WITH PARSER `ngram`;

CREATE TABLE `multi-source`.`t_search_log`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `key_words` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关键词',
  `page_type` int NULL DEFAULT NULL COMMENT '页面类型',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_delete` int NULL DEFAULT 0 COMMENT '逻辑删除0正常1删除',
  `remark` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '历史搜索记录' ROW_FORMAT = Dynamic;

CREATE TABLE `multi-source`.`t_topic_event_search`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `event_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '事件分类',
  `keywords` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关键词',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_delete` int NULL DEFAULT 0 COMMENT '逻辑删除0正常1删除',
  `remark` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '主题事件分类检索' ROW_FORMAT = Dynamic;

ALTER TABLE `multi-source`.`t_video` ADD FULLTEXT INDEX `idx_full`(`name`, `tags`) WITH PARSER `ngram`;

ALTER TABLE `multi-source`.`t_weapon` ADD FULLTEXT INDEX `idx_full`(`name`, `tags`) WITH PARSER `ngram`;

ALTER TABLE `multi-source`.`t_weapon` ADD FULLTEXT INDEX `idx_tags`(`tags`) WITH PARSER `ngram`;

SET FOREIGN_KEY_CHECKS=1;