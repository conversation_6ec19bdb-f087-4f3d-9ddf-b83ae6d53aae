package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* Created by pj on 2024/11/12
*/
@ApiModel("t_news表")
@TableName("t_news")
@Data
public class News implements Serializable {
    private String id;

    /**
     * 时间
     *
     * @mbg.generated
     */
    private Date time;

    /**
     * 标题
     *
     * @mbg.generated
     */
    private String title;

    /**
     * 来源
     *
     * @mbg.generated
     */
    private String source;

    /**
     * 标签
     *
     * @mbg.generated
     */
    private String tags;

    /**
     * 创建时间
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     * 更新时间
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * 事件抽取
     *
     * @mbg.generated
     */
    private Integer eventExtract;

    /**
     * 关键词标签
     *
     * @mbg.generated
     */
    private String keyTags;

    /**
     * 摘要
     *
     * @mbg.generated
     */
    private String summary;

    /**
     * 正文
     *
     * @mbg.generated
     */
    private String content;

    private static final long serialVersionUID = 1L;
}