package com.hzw.heterogeneous.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @datetime 2024/05/14 8:47
 * @description: 汇率dto
 * @version: 1.0
 */
@Data
public class CurrencyDto implements Serializable {

    /**
     * 货币名称
     */
    private String currencyName;

    /**
     * 现汇买入价
     */
    private BigDecimal buyingRate;

    /**
     * 现钞买入价
     */
    private BigDecimal cashPurchasePrice;

    /**
     * 现汇卖出价
     */
    private BigDecimal sellingRate;

    /**
     * 现钞卖出价
     */
    private BigDecimal cashSellingPrice;

    /**
     * 中行折算价
     */
    private BigDecimal conversionPrice;

    /**
     * 发布时间
     */
    private String publishTime;

    /**
     * 现汇买入价-计算价
     */
    private BigDecimal buyingRateForCalculate;

    /**
     * 现钞买入价-计算价
     */
    private BigDecimal cashPurchasePriceForCalculate;

    /**
     * 现汇卖出价-计算价
     */
    private BigDecimal sellingRateForCalculate;

    /**
     * 现钞卖出价-计算价
     */
    private BigDecimal cashSellingPriceForCalculate;

    /**
     * 中行折算价-计算价
     */
    private BigDecimal conversionPriceForCalculate;

}
