package com.hzw.heterogeneous.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzw.heterogeneous.controller.request.DataAccessCondition;
import com.hzw.heterogeneous.controller.request.DataAccessReq;
import com.hzw.heterogeneous.controller.request.DatabaseConnectionReq;
import com.hzw.heterogeneous.controller.request.DataSyncReq;
import com.hzw.heterogeneous.controller.response.DataAccessVo;
import com.hzw.heterogeneous.controller.response.DataSyncVo;
import com.hzw.heterogeneous.controller.response.DatabaseConnectionTestVo;
import com.hzw.heterogeneous.controller.response.SyncedDataVo;
import com.hzw.heterogeneous.model.DataAccess;
import com.hzw.heterogeneous.util.Paging;
import com.hzw.heterogeneous.util.Result;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 数据接入 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
public interface DataAccessService extends IService<DataAccess> {

    /**
     * 分页查询数据接入
     * @param condition
     * @return
     */
    IPage<DataAccessVo> listPage(DataAccessCondition condition);

    /**
     * 新增数据接入
     * @param file
     * @param fileName
     * @param fileType
     * @param fileFormat
     * @return
     */
    Result<Boolean> addDataAccess(MultipartFile file,String fileName,String fileType,String fileFormat,Integer dataType);

    /**
     * 测试数据库连接
     * @param connectionReq 数据库连接配置
     * @return 连接测试结果
     */
    Result<DatabaseConnectionTestVo> testDatabaseConnection(DatabaseConnectionReq connectionReq);

    /**
     * 执行数据同步
     * @param syncReq 数据同步请求
     * @return 同步任务信息
     */
    Result<DataSyncVo> executeDataSync(DataSyncReq syncReq);

    /**
     * 查询数据同步任务状态
     * @param taskId 任务ID
     * @return 同步状态
     */
    Result<DataSyncVo> getSyncTaskStatus(String taskId);

    /**
     * 分页查询同步的数据
     * @param targetDatabase 目标数据库名
     * @param targetTable 目标表名
     * @param current 当前页
     * @param size 每页大小
     * @return 分页数据
     */
    Result<Paging<SyncedDataVo>> listSyncedData(String targetDatabase, String targetTable, Integer current, Integer size);

    /**
     * 获取所有同步任务列表
     * @param current 当前页
     * @param size 每页大小
     * @return 同步任务列表
     */
    Result<Paging<DataSyncVo>> listSyncTasks(Integer current, Integer size);
}
