package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("t_person_trend")
public class PersonTrend {

    private String id;

    private String personId;

    /**
     * 时间
     */
    private Date time;

    /**
     * 地点
     */
    private String location;

    /**
     * 动向类型
     */
    private String trendType;


    /**
     * 全文内容
     */
    private String fullText;
    
    /**
     * 标签
     */
    private String tags;
    
    /**
     * 创建时间
     */
    private Date createdTime;
    
    /**
     * 更新时间
     */
    private Date updatedTime;
    
    /**
     * 关联的事件列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<Events> relatedEvents;

}
