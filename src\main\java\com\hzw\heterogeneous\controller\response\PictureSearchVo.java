package com.hzw.heterogeneous.controller.response;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @datetime 2024/05/14 14:15
 * @description: 图片检索vo
 * @version: 1.0
 */
@Data
public class PictureSearchVo implements Serializable {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "文件路径")
    private String url;

    @ApiModelProperty(value = "文件base64")
    private String base64;

    @ApiModelProperty(value = "文件名称")
    private String name;

    @ApiModelProperty(value = "标签")
    private String tags;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    private Double score;

    private Integer type;
}
