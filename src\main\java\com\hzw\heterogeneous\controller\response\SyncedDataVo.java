package com.hzw.heterogeneous.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 同步数据查询响应
 * <AUTHOR>
 * @date 2025-08-31
 */
@ApiModel("同步数据查询响应")
@Data
public class SyncedDataVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录ID")
    private String id;

    @ApiModelProperty(value = "源数据（JSON格式）")
    private String sourceData;

    @ApiModelProperty(value = "目标数据（JSON格式）")
    private String targetData;

    @ApiModelProperty(value = "同步时间")
    private Date syncTime;

    @ApiModelProperty(value = "数据来源表")
    private String sourceTable;

    @ApiModelProperty(value = "目标表")
    private String targetTable;

    @ApiModelProperty(value = "同步任务ID")
    private String taskId;
}