package com.hzw.heterogeneous.util;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.corpus.tag.Nature;
import com.hankcs.hanlp.seg.common.Term;
import org.apache.commons.lang3.StringUtils;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @datetime 2024/4/8 14:49
 * @description: 文本相似度对比
 * @version: 1.0
 */
public class NlpUtils {

    // 提取文本中有实意的词
    public static List<String> extractWordFromText(String text){
        // resultList 用于保存提取后的结果
        List<String> resultList = new ArrayList<>();
        // 当 text 为空字符串时，使用分词函数会报错，所以需要提前处理这种情况
        if(text.length() == 0){
            return resultList;
        }
        
        // 分词
        List<Term> termList = HanLP.segment(text);
        
        // 提取有实意的词汇，优化搜索相关性
        for (Term term : termList) {
            String word = term.word;
            Nature nature = term.nature;
            
            // 过滤掉无意义的词汇
            if (isStopWord(word)) {
                continue;
            }
            
            // 人名识别增强：优先处理可能的人名
            if (isPotentialPersonName(word) || nature == Nature.nr || 
                nature == Nature.n || nature == Nature.ns || 
                nature == Nature.nt || nature == Nature.nz || nature == Nature.a || 
                nature == Nature.m || nature == Nature.t ||
                (nature == Nature.vn) ||
                (nature == Nature.v && !isIntentVerb(word))) {
                resultList.add(word);
            }
        }
        
        // 实体识别补偿机制：对于可能遗漏的人名进行补强
        List<String> enhancedEntities = enhanceEntityRecognition(text, resultList);
        resultList.addAll(enhancedEntities);
        
        // 去重处理
        return resultList.stream().distinct().collect(Collectors.toList());
    }
    
    /**
     * 判断是否为潜在的人名
     * @param word 词汇
     * @return 是否为潜在人名
     */
    private static boolean isPotentialPersonName(String word) {
        if (StringUtils.isBlank(word) || word.length() < 2) {
            return false;
        }
        
        // 常见的外国人名特征
        Set<String> foreignNameIndicators = Set.of(
            // 美国政治人物
            "特朗普", "拜登", "奥巴马", "克林顿", "布什", "里根", "卡特", "福特",
            "尼克松", "约翰逊", "肯尼迪", "艾森豪威尔", "杜鲁门", "罗斯福",
            // 欧洲政治人物
            "默克尔", "马克龙", "普京", "泽连斯基", "梅", "卡梅伦",
            "撒切尔", "布莱尔", "梅德韦杰夫", "叶利钦", "戈尔巴乔夫",
            // 科技界人物
            "马斯克", "贝佐斯", "盖茨", "乔布斯", "库克", "扎克伯格", "佩奇", "布林",
            "皮查伊", "纳德拉", "贝尼奥夫", "埃里森", "戴尔", "惠普",
            // 娱乐界人物
            "汤姆", "约翰", "詹姆斯", "迈克尔", "威廉", "大卫", "罗伯特", "理查德",
            "玛丽", "安娜", "莎拉", "艾米", "琳达", "苏珊", "海伦", "南希",
            // 体育界人物  
            "梅西", "C罗", "内马尔", "姆巴佩", "哈兰德", "莱万", "本泽马", "德布劳内",
            "库里", "杜兰特", "字母哥", "东契奇", "约基奇", "恩比德"
        );
        
        // 直接匹配已知人名
        if (foreignNameIndicators.contains(word)) {
            return true;
        }
        
        // 外国人名的音译特征判断
        return hasForeignNameCharacteristics(word);
    }
    
    /**
     * 判断是否具有外国人名音译特征
     * @param word 词汇
     * @return 是否具有外国人名特征
     */
    private static boolean hasForeignNameCharacteristics(String word) {
        // 常见的音译字符，使用HashSet避免重复元素问题
        Set<Character> transliterationChars = new HashSet<>(Arrays.asList(
            // 常见音译用字
            '特', '拉', '克', '斯', '尔', '德', '里', '奇', '夫', '娜', '妮', '丝', '莎',
            '亚', '欧', '罗', '巴', '卡', '普', '朗', '汤', '姆', '约', '翰', '威', '廉',
            '大', '卫', '伯', '理', '查', '玛', '安', '琳', '海', '南', '希',
            '马', '龙', '默', '泽', '连', '基', '逊', '梅', '卡', '梅', '伦',
            '布', '莱', '撒', '切', '戈', '乔', '叶', '钦', '韦', '杰', '科',
            '贝', '佐', '盖', '茨', '库', '扎', '格', '佩', '奇',
            '林', '皮', '伊', '纳', '尼', '奥', '埃', '戴', '乔', '布'
        ));
        
        // 检查是否包含多个音译字符
        long transliterationCount = word.chars()
                .mapToObj(c -> (char) c)
                .filter(transliterationChars::contains)
                .count();
        
        // 如果包含2个或以上音译字符，且长度在2-4之间，可能是外国人名
        return transliterationCount >= 2 && word.length() >= 2 && word.length() <= 4;
    }
    
    /**
     * 实体识别补偿机制
     * @param originalText 原始文本
     * @param extractedWords 已提取的词汇
     * @return 补偿的实体列表
     */
    private static List<String> enhanceEntityRecognition(String originalText, List<String> extractedWords) {
        List<String> enhancedEntities = new ArrayList<>();
        
        // 对原文进行正则匹配，寻找可能遗漏的人名
        // 匹配2-4个汉字的连续序列，且包含外国人名特征字符
        String namePattern = "[\u4e00-\u9fa5]{2,4}";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(namePattern);
        java.util.regex.Matcher matcher = pattern.matcher(originalText);
        
        while (matcher.find()) {
            String candidate = matcher.group();
            
            // 如果已经提取过或者是停用词，跳过
            if (extractedWords.contains(candidate) || isStopWord(candidate) || isIntentVerb(candidate)) {
                continue;
            }
            
            // 如果是潜在人名且未被提取，加入补偿列表
            if (isPotentialPersonName(candidate)) {
                enhancedEntities.add(candidate);
            }
        }
        
        return enhancedEntities;
    }
    
    /**
     * 判断是否为停用词
     * @param word 词汇
     * @return 是否为停用词
     */
    private static boolean isStopWord(String word) {
        // 常见的停用词列表
        Set<String> stopWords = Set.of(
            "我", "你", "他", "她", "它", "我们", "你们", "他们", "她们", "它们",
            "这", "那", "这个", "那个", "这些", "那些", "这里", "那里",
            "的", "了", "在", "是", "有", "和", "与", "及", "或", "但", "而",
            "因为", "所以", "如果", "虽然", "然而", "因此", "由于", "关于",
            "对于", "根据", "按照", "通过", "经过", "为了", "以便", "以及",
            "一个", "一些", "几个", "许多", "很多", "非常", "十分", "比较",
            "还", "也", "都", "才", "就", "只", "仅", "刚", "正"
        );
        return stopWords.contains(word);
    }
    
    /**
     * 判断是否为意图动词（搜索意图相关的动词，对搜索结果意义不大）
     * @param word 词汇
     * @return 是否为意图动词
     */
    private static boolean isIntentVerb(String word) {
        Set<String> intentVerbs = Set.of(
            "想", "要", "需要", "希望", "打算", "准备", "计划",
            "查找", "搜索", "寻找", "查询", "检索", "搜寻",
            "看", "听", "读", "观看", "浏览", "阅读",
            "了解", "知道", "认识", "学习", "研究"
        );
        return intentVerbs.contains(word);
    }
    
    public static List<String> extractMeaningfulWords(String text) {
        // 1. 分词并标注词性
        List<Term> termList = HanLP.segment(text);

        // 2. 过滤保留实意词（名词、动词、形容词等）
        return termList.stream()
                .filter(term -> {
                    String pos = term.nature.toString();
                    return pos.startsWith("n") ||  // 名词
                            pos.startsWith("v") ||  // 动词
                            pos.startsWith("a") ||  // 形容词
                            pos.startsWith("m") ||  // 数词
                            pos.startsWith("t");    // 时间词
                })
                .map(term -> term.word)            // 提取词本身
                .collect(Collectors.toList());
    }

//    public static void main(String[] args) {
//        HanLP.Config.ShowTermNature = false;
//        String text = "我是中国人";
//        List<String> resultList = NlpUtils.extractWordFromText(text);
//        System.out.println(resultList);
//        resultList=HanLP.segment(text).stream()
//                .map(term -> term.word + "/" +term.nature)
//                .collect(Collectors.toList());
//        System.out.println(resultList);
//        resultList=HanLP.segment(text).stream()
//                .filter(term -> term.nature.toString().startsWith("nr") || term.nature.toString().startsWith("ns"))
//                .map(term -> term.word + "(" + term.nature + ")")
//                .collect(Collectors.toList());
//        System.out.println(resultList);
//         text = "今天天气晴朗，张三决定去清华大学图书馆看书。";
//        List<String> meaningfulWords = extractMeaningfulWords(text);
//        System.out.println(meaningfulWords);
//    }
}
