package com.hzw.heterogeneous.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.request.PictureSearchCondition;
import com.hzw.heterogeneous.controller.response.PictureSearchVo;
import com.hzw.heterogeneous.service.PictureSearchService;
import com.hzw.heterogeneous.util.Paging;
import com.hzw.heterogeneous.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * @datetime 2024/11/11 9:06
 * @description: 图片检索服务
 * @version: 1.0
 */
@Api(tags = "图片检索")
@RestController
@RequestMapping("/pictureSearch")
public class PictureSearchController {

    @Autowired
    private PictureSearchService pictureSearchService;

    @ApiOperation(value = "分页查询图片数据")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "CurrencyCondition", paramType = "body")
    @PostMapping(value = "/listPage")
    public Result<Paging<PictureSearchVo>> listPage(@RequestBody PictureSearchCondition condition) {
        IPage<PictureSearchVo> page = pictureSearchService.listPage(condition);
        return Result.ok(Paging.buildPaging(page));
    }


    /**
     * 上传图片到一个临时文件夹，然后将图片地址作为参数，调用一个外部接口获取一个图片列表
     */
    @ApiOperation("上传图片并检索")
    @PostMapping("/uploadAndSearch")
    public Result<Object> uploadAndSearch(@RequestParam("file") MultipartFile file) {


        try {
            IPage<PictureSearchVo> searchResults = pictureSearchService.uploadImageAndCallApi(file);
            return Result.ok(Paging.buildPaging(searchResults));
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("图片处理失败: " + e.getMessage());
        }
    }

}
