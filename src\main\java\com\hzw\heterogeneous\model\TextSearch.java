package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2024-05-14
 */
@Getter
@Setter
@TableName("")
@ApiModel(value = "TextSearch对象", description = "")
public class TextSearch implements Serializable {

    private static final long serialVersionUID = 1L;

}
