<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.heterogeneous.mapper.TopicEventSearchMapper">

    <select id="listPage" resultType="com.hzw.heterogeneous.controller.response.TopicEventSearchVo">
        SELECT
            t1.*,
            (0
             <if test="condition.keyWords != null and condition.keyWords != ''">
                + MATCH(t1.title, t1.content, t1.tags) AGAINST (#{condition.keyWords} IN BOOLEAN MODE)
             </if>
             <if test="condition.words != null and condition.words != ''">
                + MATCH(t1.title, t1.content, t1.tags) AGAINST (#{condition.words} IN BOOLEAN MODE)
             </if>) AS total_relevance
        FROM
            t_events t1
        WHERE
            1=1
            <if test="null != condition.type and condition.type != ''">
                AND t1.type = #{condition.type}
            </if>
            <if test="condition.words != null and condition.words != ''">
                AND MATCH(t1.title, t1.content, t1.tags) AGAINST (#{condition.words} IN BOOLEAN MODE)
            </if>
            <if test="condition.keyWords != null and condition.keyWords != ''">
                AND MATCH(t1.title, t1.content, t1.tags) AGAINST (#{condition.keyWords} IN BOOLEAN MODE)
            </if>
        <if test="null == condition.orderType or condition.orderType == 1">
            ORDER BY total_relevance
            <if test="null == condition.orderRule or condition.orderRule == 1">
                DESC
            </if>
            <if test="null != condition.orderRule and condition.orderRule == 2">
                ASC
            </if>
        </if>
        <if test="null != condition.orderType and condition.orderType == 2">
            ORDER BY t1.time
            <if test="null == condition.orderRule or condition.orderRule == 1">
                DESC
            </if>
            <if test="null != condition.orderRule and condition.orderRule == 2">
                ASC
            </if>
        </if>
    </select>
    <select id="getCount" resultType="java.lang.Integer">
        SELECT
            COUNT(1) AS num
        FROM
            t_events t1
        WHERE
            1=1
        <if test="words != null and words != ''">
            AND (
<!--                <foreach collection="words" item="word" separator="OR">-->
                    MATCH(t1.title, t1.content,t1.tags) AGAINST (#{words} IN BOOLEAN MODE)
<!--                </foreach>-->
            )
        </if>
    </select>
</mapper>