package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* Created by pj on 2024/11/12
*/
@ApiModel("t_search_log表")
@TableName("t_search_log")
@Data
public class SearchLog implements Serializable {
    private String id;

    /**
     * 关键词
     *
     * @mbg.generated
     */
    private String keyWords;

    /**
     * 页面类型
     *
     * @mbg.generated
     */
    private Integer pageType;

    /**
     * 创建时间
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     * 更新时间
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * 逻辑删除0正常1删除
     *
     * @mbg.generated
     */
    private Integer isDelete;

    /**
     * 备注
     *
     * @mbg.generated
     */
    private String remark;

    private static final long serialVersionUID = 1L;
}