package com.hzw.heterogeneous.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.Instant;
import java.time.temporal.ChronoUnit;

@Slf4j
@Service
public class TempFileCleanupServiceImpl {

    @Value("${api.image.match.tmpDirPath}")
    private String tmpDirPath;
    // 2. 清理间隔（天）
    @Value("${api.image.match.cleanup.days:3}")
    private int cleanDays;

    // 3. 定时任务 cron 表达式
    @Value("${api.image.match.cleanup.cron:0 0 2 * * ?}")
    private String cleanupCron;
    // 每天凌晨2点执行清理

    // 使用 SpEL 动态绑定 cron 表达式
    @Scheduled(cron = "${api.image.match.cleanup.cron:0 0 2 * * ?}")
    public void cleanOldTempFiles() {
        File tempDir = new File(tmpDirPath);
        if (!tempDir.exists()) {
            log.info("临时目录不存在，跳过清理: {}", tempDir.getAbsolutePath());
            return;
        }

        File[] files = tempDir.listFiles();
        if (files == null || files.length == 0) {
            log.info("临时目录无文件需要清理");
            return;
        }

        // 根据配置计算清理时间阈值
        long cutoffTime = Instant.now().minus(cleanDays, ChronoUnit.DAYS).toEpochMilli();

        for (File file : files) {
            if (file.lastModified() < cutoffTime) {
                if (file.delete()) {
                    log.info("清理成功: {}", file.getAbsolutePath());
                } else {
                    log.warn("清理失败: {}", file.getAbsolutePath());
                }
            }
        }
    }
}
