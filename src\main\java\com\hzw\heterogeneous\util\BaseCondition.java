package com.hzw.heterogeneous.util;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hzw.heterogeneous.constant.FormatConstants;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 查询条件的基础类，用于继承
 *
 * <AUTHOR>
 * @version 1.0.0 2021-03-31
 */
public abstract class BaseCondition extends PagingCondition {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -2369459314543352900L;

    /**
     * 创建查询条件构造器，将对象或Map转Bean对象，
     * 并把对象或Map中的值拷贝给Bean对象，拷贝进来的这些值会自动产生查询条件
     *
     * @param clazz 目标的Bean类型
     * @return 查询条件构造器
     */
    public <T> QueryWrapper<T> buildQueryWrapper(Class<T> clazz) {
        T entity = BeanUtil.toBean(this, clazz);
        return order(new QueryWrapper<>(entity));
    }


    /**
     * 创建查询条件构造器，不会自动产生查询条件
     *
     * @return 查询条件构造器
     */
    public <T> QueryWrapper<T> buildQueryWrapper() {
        return order(new QueryWrapper<>());
    }

    private <T> QueryWrapper<T> order(QueryWrapper<T> objectQueryWrapper) {
        List<String> sortField = this.getSortField();
        List<Boolean> sortOrder = this.getSortOrder();
        if (!CollectionUtils.isEmpty(sortField) && !CollectionUtils.isEmpty(sortOrder) && sortField.size() == sortOrder.size()) {
            for (int i = 0; i < sortField.size(); i++) {
                String field = sortField.get(i);
                Boolean order = sortOrder.get(i);
                if (order) {
                    objectQueryWrapper.orderByAsc(field);
                } else {
                    objectQueryWrapper.orderByDesc(field);
                }
            }
            return objectQueryWrapper;
        } else {
            objectQueryWrapper.orderByDesc(FormatConstants.CREATED_TIME);
            return objectQueryWrapper;
        }
    }
}
