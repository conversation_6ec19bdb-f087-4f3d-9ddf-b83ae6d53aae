package com.hzw.heterogeneous.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * AI服务客户端
 * 提供语种识别、文本翻译、文件格式识别、文本抽取、人脸识别、实体抽取等AI功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
//@Component
public class AIServiceClient {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /** 连接超时时间（毫秒） */
    private static final int CONNECTION_TIMEOUT = 30000; // 30秒
    
    /** 读取超时时间（毫秒） */
    private static final int SOCKET_TIMEOUT = 60000; // 60秒
    
    /** 请求超时时间（毫秒） */
    private static final int REQUEST_TIMEOUT = 10000; // 10秒
    
    /** AI服务基础URL */
    private final String baseUrl;
    
    /** HTTP客户端配置 */
    private final RequestConfig requestConfig;

    public AIServiceClient(String baseUrl) {
        if (StrUtil.isBlank(baseUrl)) {
            throw new IllegalArgumentException("AI服务基础URL不能为空");
        }
        this.baseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        
        // 配置HTTP超时参数
        this.requestConfig = RequestConfig.custom()
                .setConnectTimeout(CONNECTION_TIMEOUT)          // 连接超时
                .setSocketTimeout(SOCKET_TIMEOUT)              // 读取超时
                .setConnectionRequestTimeout(REQUEST_TIMEOUT)  // 请求超时
                .build();
                
        log.info("初始AI服务客户端，基础URL: {}", this.baseUrl);
        log.info("超时配置 - 连接超时: {}ms, 读取超时: {}ms, 请求超时: {}ms", 
                CONNECTION_TIMEOUT, SOCKET_TIMEOUT, REQUEST_TIMEOUT);
    }

    /**
     * 语种识别接口
     * 
     * @param text 需要识别的文本内容
     * @return 识别结果，包含语种信息
     * @throws IOException 网络异常或响应解析异常
     */
    public Map<String, Object> detectLanguage(String text) throws IOException {
        if (StrUtil.isBlank(text)) {
            throw new IllegalArgumentException("输入文本不能为空");
        }
        
        log.info("开始语种识别，文本长度: {}", text.length());
        
        Map<String, String> params = new HashMap<>();
        params.put("text", text);
        
        Map<String, Object> result = postJsonRequest("/detect_language", params);
        log.info("语种识别完成，结果: {}", JSONUtil.toJsonStr(result));
        
        return result;
    }

    /**
     * 文本翻译接口
     * 
     * @param text 需要翻译的文本内容
     * @param sourceLang 源语言代码（如: zh, en, ja）
     * @param targetLang 目标语言代码（如: zh, en, ja）
     * @return 翻译结果
     * @throws IOException 网络异常或响应解析异常
     */
    public Map<String, Object> translateText(String text, String sourceLang, String targetLang) throws IOException {
        if (StrUtil.hasBlank(text, sourceLang, targetLang)) {
            throw new IllegalArgumentException("文本内容、源语言和目标语言都不能为空");
        }
        log.debug("开始翻译：{}",text);
        log.info("开始文本翻译，文本长度: {}, 源语言: {}, 目标语言: {}", text.length(), sourceLang, targetLang);
        
        Map<String, String> params = new HashMap<>();
        params.put("text", text);
        params.put("source_lang", sourceLang);
        params.put("target_lang", targetLang);
        
        Map<String, Object> result = postJsonRequest("/translate", params);
        log.info("文本翻译完成，结果: {}", JSONUtil.toJsonStr(result));
        
        return result;
    }

    /**
     * 文件格式识别接口
     * 
     * @param file 需要识别格式的文件
     * @return 文件格式识别结果
     * @throws IOException 网络异常或响应解析异常
     */
    public Map<String, Object> detectFileFormat(File file) throws IOException {
        validateFile(file);
        
        log.info("开始文件格式识别，文件: {}, 大小: {} bytes", file.getName(), file.length());
        
        Map<String, Object> result = postFileRequest("/detect_file_format", file);
        log.info("文件格式识别完成，结果: {}", JSONUtil.toJsonStr(result));
        
        return result;
    }

    /**
     * 文本内容抽取接口
     * 
     * @param file 需要抽取文本的文件
     * @return 文本抽取结果
     * @throws IOException 网络异常或响应解析异常
     */
    public Map<String, Object> extractFileText(File file) throws IOException {
        validateFile(file);
        
        log.info("开始文本内容抽取，文件: {}, 大小: {} bytes", file.getName(), file.length());
        
        Map<String, Object> result = postFileRequest("/extract_file_text", file);
        log.info("文本内容抽取完成，结果数据长度: {}", 
                result.get("text") != null ? result.get("text").toString().length() : 0);
        
        return result;
    }

    /**
     * 人脸识别接口
     * 
     * @param imageFile 需要识别人脸的图片文件
     * @return 人脸识别结果
     * @throws IOException 网络异常或响应解析异常
     */
    public Map<String, Object> recognizeFace(File imageFile) throws IOException {
        validateFile(imageFile);
        
        log.info("开始人脸识别，图片文件: {}, 大小: {} bytes", imageFile.getName(), imageFile.length());
        
        Map<String, Object> result = postFileRequest("/face_recognize", imageFile);
        log.info("人脸识别完成，检测到人脸数量: {}", 
                result.get("total_faces") != null ? result.get("total_faces") : 0);
        
        return result;
    }

    /**
     * 实体抽取接口
     * 
     * @param text 需要抽取实体的文本内容
     * @return 实体抽取结果
     * @throws IOException 网络异常或响应解析异常
     */
    public Map<String, Object> extractEntities(String text) throws IOException {
        if (StrUtil.isBlank(text)) {
            throw new IllegalArgumentException("输入文本不能为空");
        }
        
        log.info("开始实体抽取，文本长度: {}", text.length());
        
        Map<String, String> params = new HashMap<>();
        params.put("text", text);
        
        Map<String, Object> result = postJsonRequest("/extract_entities", params);
        log.info("实体抽取完成，结果: {}", JSONUtil.toJsonStr(result));
        
        return result;
    }

    /**
     * 文件有效性验证
     * 
     * @param file 需要验证的文件
     * @throws IllegalArgumentException 文件不合法时抛出
     */
    private void validateFile(File file) {
        if (file == null) {
            throw new IllegalArgumentException("文件不能为null");
        }
        if (!file.exists()) {
            throw new IllegalArgumentException("文件不存在: " + file.getAbsolutePath());
        }
        if (!file.isFile()) {
            throw new IllegalArgumentException("路径不是一个文件: " + file.getAbsolutePath());
        }
        if (file.length() == 0) {
            throw new IllegalArgumentException("文件为空: " + file.getName());
        }
        // 限制文件大小为100MB
        long maxFileSize = 100 * 1024 * 1024; // 100MB
        if (file.length() > maxFileSize) {
            throw new IllegalArgumentException(
                    String.format("文件大小超出限制，当前: %d bytes, 最大允许: %d bytes", 
                            file.length(), maxFileSize));
        }
    }

    /**
     * 通用JSON请求方法
     * 
     * @param endpoint API端点路径
     * @param params 请求参数
     * @return 响应结果
     * @throws IOException 网络异常或响应解析异常
     */
    private Map<String, Object> postJsonRequest(String endpoint, Map<String, String> params) throws IOException {
        String requestUrl = baseUrl + endpoint;
        long startTime = System.currentTimeMillis();
        
        log.debug("发送JSON请求到: {}, 参数: {}", requestUrl, JSONUtil.toJsonStr(params));
        
        try (CloseableHttpClient httpClient = createHttpClient()) {
            HttpPost httpPost = new HttpPost(requestUrl);
            httpPost.setConfig(requestConfig);
            
            // 明确指定UTF-8编码
            String json = objectMapper.writeValueAsString(params);
            StringEntity entity = new StringEntity(json, StandardCharsets.UTF_8);
            entity.setContentType("application/json; charset=UTF-8");
            httpPost.setEntity(entity);
            
            // 设置请求头
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("User-Agent", "HeterogeneousAPI-Client/1.0");

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                long duration = System.currentTimeMillis() - startTime;
                log.debug("请求完成，耗时: {}ms, 状态码: {}", duration, response.getStatusLine().getStatusCode());
                
                return parseResponse(response, requestUrl);
            }
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("请求失败，URL: {}, 耗时: {}ms, 错误: {}", requestUrl, duration, e.getMessage(), e);
            throw new IOException("请求AI服务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用文件上传方法
     * 
     * @param endpoint API端点路径
     * @param file 需要上传的文件
     * @return 响应结果
     * @throws IOException 网络异常或响应解析异常
     */
    private Map<String, Object> postFileRequest(String endpoint, File file) throws IOException {
        String requestUrl = baseUrl + endpoint;
        long startTime = System.currentTimeMillis();
        
        log.debug("发送文件请求到: {}, 文件: {}, 大小: {} bytes", requestUrl, file.getName(), file.length());
        
        try (CloseableHttpClient httpClient = createHttpClient()) {
            HttpPost httpPost = new HttpPost(requestUrl);
            httpPost.setConfig(requestConfig);
            
            // 构建多部分请求
            MultipartEntityBuilder builder = MultipartEntityBuilder.create()
                    .addBinaryBody("file", file, ContentType.APPLICATION_OCTET_STREAM, file.getName());
            httpPost.setEntity(builder.build());
            
            // 设置请求头
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("User-Agent", "HeterogeneousAPI-Client/1.0");

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                long duration = System.currentTimeMillis() - startTime;
                log.debug("文件请求完成，耗时: {}ms, 状态码: {}", duration, response.getStatusLine().getStatusCode());
                
                return parseResponse(response, requestUrl);
            }
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("文件请求失败，URL: {}, 文件: {}, 耗时: {}ms, 错误: {}", 
                    requestUrl, file.getName(), duration, e.getMessage(), e);
            throw new IOException("上传文件到AI服务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建HTTP客户端
     * 
     * @return 配置好的HTTP客户端
     */
    private CloseableHttpClient createHttpClient() {
        return HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setMaxConnTotal(20)           // 最大连接数
                .setMaxConnPerRoute(10)        // 每个路由最大连接数
                .build();
    }

    /**
     * 解析响应结果
     * 
     * @param response HTTP响应
     * @param requestUrl 请求URL（用于错误日志）
     * @return 解析后的响应数据
     * @throws IOException 响应解析异常
     */
    private Map<String, Object> parseResponse(CloseableHttpResponse response, String requestUrl) throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();
        
        // 检查HTTP状态码
        if (statusCode < 200 || statusCode >= 300) {
            String errorMsg = String.format("请求失败，HTTP状态码: %d, URL: %s", statusCode, requestUrl);
            log.error(errorMsg);
            throw new IOException(errorMsg);
        }
        
        HttpEntity entity = response.getEntity();
        if (entity == null) {
            String errorMsg = "服务器返回空响应";
            log.error(errorMsg);
            throw new IOException(errorMsg);
        }
        
        try {
            // 明确指定UTF-8编码
            String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            
            if (StrUtil.isBlank(result)) {
                String errorMsg = "服务器返回空内容";
                log.error(errorMsg);
                throw new IOException(errorMsg);
            }
            
            log.debug("收到响应数据长度: {} 字符", result.length());
            
            @SuppressWarnings("unchecked")
            Map<String, Object> resultMap = objectMapper.readValue(result, Map.class);
            
            return resultMap;
            
        } catch (Exception e) {
            String errorMsg = "解析响应JSON失败: " + e.getMessage();
            log.error(errorMsg, e);
            throw new IOException(errorMsg, e);
        }
    }
    /**
     * 示例主方法，用于测试AI服务客户端
     */
    public static void main(String[] args) {
        AIServiceClient client = new AIServiceClient("http://61.132.53.18:8001");

        try {
            log.info("开始测试AI服务客户端...");
            
            // 1. 语种识别测试
            log.info("=== 测试语种识别 ===");
            Map<String, Object> langResult = client.detectLanguage("Hello World");
            log.info("语种识别结果: {}", JSONUtil.toJsonStr(langResult));

            // 2. 文本翻译测试
            log.info("=== 测试文本翻译 ===");
            Map<String, Object> transResult = client.translateText("你好", "zh", "en");
            log.info("翻译结果: {}", transResult.get("translation"));

            // 3. 实体抽取测试
            log.info("=== 测试实体抽取 ===");
            Map<String, Object> entityResult = client.extractEntities("美国总统拜登访问乌克兰");
            log.info("实体抽取结果: {}", entityResult.get("results"));
            
            log.info("所有测试完成！");

        } catch (IOException e) {
            log.error("测试过程中发生异常", e);
        } catch (Exception e) {
            log.error("未知异常", e);
        }
    }
}
