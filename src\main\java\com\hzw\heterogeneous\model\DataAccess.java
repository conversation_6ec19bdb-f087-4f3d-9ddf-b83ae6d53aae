package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* Created by cx on 2024/11/12
*/
@ApiModel("数据接入表")
@TableName("t_data_access")
@Data
public class DataAccess implements Serializable {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    private String fileUrl;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "文件格式")
    private String fileFormat;

    @ApiModelProperty(value = "数据接入类型")
    private Integer dataType;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;
    /**
     * 更新时间
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
    @ApiModelProperty(value = "逻辑删除0正常1删除")
    private Integer isDelete;

    @ApiModelProperty(value = "备注")
    private String remark;


    private static final long serialVersionUID = 1L;
}
