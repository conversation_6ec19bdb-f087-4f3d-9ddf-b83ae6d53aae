package com.hzw.heterogeneous.service;

import com.hzw.heterogeneous.controller.response.EntityTypeNumVo;
import com.hzw.heterogeneous.controller.response.PersonVo;
import com.hzw.heterogeneous.model.Events;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import com.hzw.heterogeneous.util.Paging;

import java.util.List;

public interface PersonService {
    /**
     * 获取各个类型任务数量
     * @param condition
     * @return
     */
    List<EntityTypeNumVo> listPersonNum(ThematicAnalysisCondition condition);

    /**
     * 分页获取重要人物数据
     * @param condition
     * @return
     */
    Paging<PersonVo> listPersonPage(ThematicAnalysisCondition condition);

    /**
     * 获取人物详情
     * @param condition
     * @return
     */
    PersonVo getPersonInfo(ThematicAnalysisCondition condition);

    /**
     * 导出重要人物数据
     * @param ids
     * @return
     */
    List<PersonVo> exportList(List<String> ids);
    
    /**
     * 获取指定动向的关联事件列表
     * @param trendId 动向ID
     * @return 关联的事件列表，按时间先后顺序排序
     */
    List<Events> getTrendRelatedEvents(String trendId);
}
