<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.heterogeneous.mapper.PictureSearchMapper">

    <!-- 分页检索图片信息 -->
    <select id="listPicturePage" resultType="com.hzw.heterogeneous.controller.response.PictureSearchVo">
        SELECT
        id,
        name,
        url,
        tags,
        <if test="null != condition.keyWords and '' != condition.keyWords ">
            MATCH(name,tags) AGAINST (#{condition.keyWords} IN BOOLEAN MODE) as relevance,
        </if>
        date_format(created_time,'%Y-%m-%d %H:%i:%s')
        FROM t_picture
        where 1=1
        <if test="null != condition.keyWords and '' != condition.keyWords ">
            and MATCH(name,tags) AGAINST (#{condition.keyWords} IN BOOLEAN MODE)
        </if>
        <if test="null != condition.picType and '' != condition.picType ">
            and type = #{condition.picType}
        </if>
        <if test="null == condition.orderType and condition.orderType == 1">
            ORDER BY relevance DESC
        </if>
        <if test="null != condition.orderType and condition.orderType == 2">
            ORDER BY created_time DESC
        </if>
    </select>
    <select id="listPicturePageByPerson"
            resultType="com.hzw.heterogeneous.controller.response.PictureSearchVo">
        SELECT
            t3.id,
            t3.user_name name,
            t1.url,
            t1.tags,
            <if test="null != condition.keyWords and '' != condition.keyWords ">
                MATCH(t3.user_name, t3.tags) AGAINST (#{condition.keyWords} IN BOOLEAN MODE) as relevance,
            </if>
            date_format(t1.created_time,'%Y-%m-%d %H:%i:%s')
        FROM t_picture t1
        left join t_person_weapon_picture_rel t2 on t2.picture_id = t1.id
        left join t_person t3 on t2.person_weapon_id = t3.id
        where t1.type = 1
        and t2.picture_type = 1
        <if test="null != condition.keyWords and '' != condition.keyWords ">
            and MATCH(t3.user_name, t3.tags) AGAINST (#{condition.keyWords} IN BOOLEAN MODE)
        </if>
        <if test="null == condition.orderType and condition.orderType == 1">
            ORDER BY relevance DESC
        </if>
        <if test="null != condition.orderType and condition.orderType == 2">
            ORDER BY t1.created_time DESC
        </if>
    </select>
    <select id="listPicturePageByWeapon"
            resultType="com.hzw.heterogeneous.controller.response.PictureSearchVo">
        SELECT
        t3.id,
        t3.name as name,
        t1.url,
        t1.tags,
        <if test="null != condition.keyWords and '' != condition.keyWords ">
            MATCH(t3.NAME, t3.tags) AGAINST (#{condition.keyWords} IN BOOLEAN MODE) as relevance,
        </if>
        date_format(t1.created_time,'%Y-%m-%d %H:%i:%s')
        FROM t_picture t1
        left join t_person_weapon_picture_rel t2 on t2.picture_id = t1.id
        left join t_weapon t3 on t2.person_weapon_id = t3.id
        where t1.type = 2
        and t2.picture_type = 2
        <if test="null != condition.keyWords and '' != condition.keyWords ">
            and MATCH(t3.NAME, t3.tags ) AGAINST (#{condition.keyWords} IN BOOLEAN MODE)
        </if>
        <if test="null == condition.orderType and condition.orderType == 1">
            ORDER BY relevance DESC
        </if>
        <if test="null != condition.orderType and condition.orderType == 2">
            ORDER BY t1.created_time DESC
        </if>
    </select>


</mapper>
