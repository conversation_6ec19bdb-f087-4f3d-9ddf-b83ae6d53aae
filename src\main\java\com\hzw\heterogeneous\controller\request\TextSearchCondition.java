package com.hzw.heterogeneous.controller.request;

import com.hzw.heterogeneous.util.BaseCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class TextSearchCondition extends BaseCondition {

    @ApiModelProperty(value = "文本类型 1.新闻与媒体 2.文献")
    private Integer textType;

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "普通搜索关键字")
    private String keyWords;

    @ApiModelProperty(value = "高级搜索关键字1")
    private String keyWords1;

    @ApiModelProperty(value = "高级搜索关键字2")
    private String keyWords2;

    @ApiModelProperty(value = "高级搜索关键字3")
    private String keyWords3;

    @ApiModelProperty(value = "意图搜索句子")
    private String keySentence;

    @ApiModelProperty(value = "搜索关键字传参")
    private String keyWordsParam;

    @ApiModelProperty(value = "起始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "排序方式  1 相关度  2 时间")
    private Integer orderType;

}
