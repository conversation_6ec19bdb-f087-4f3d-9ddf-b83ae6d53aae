package com.hzw.heterogeneous.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 综合搜索结果响应对象
 */
@Data
@ApiModel("综合搜索结果")
public class ComprehensiveSearchVo {
    
    @ApiModelProperty("搜索结果列表")
    private List<SearchResultItem> data;
    
    /**
     * 搜索结果项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel("搜索结果项")
    public static class SearchResultItem {
        
        @ApiModelProperty("关键词或名称")
        private String keyWords;
        
        @ApiModelProperty("类型：1-日志，2-武器，3-人物")
        private Integer type;
    }
}