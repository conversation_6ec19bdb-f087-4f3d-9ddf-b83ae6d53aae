# 数据同步API使用指南

## 概述

数据同步功能允许将其他数据源（MySQL、Oracle、MongoDB）的数据同步到当前的MySQL数据库中。类似于Navicat的数据同步功能，支持全量同步和增量同步。

## API接口说明

### 1. 测试数据库连接

**接口地址**: `POST /heterogeneous/dataAccess/testConnection`

**功能**: 测试源数据库连接是否正常，并返回可用表列表

**请求参数**:
```json
{
  "dbType": 1,                    // 数据库类型：1-MySQL, 2-Oracle, 3-MongoDB
  "host": "127.0.0.1",           // 数据库地址
  "port": 3306,                  // 数据库端口
  "database": "test_db",         // 数据库名称
  "username": "root",            // 用户名
  "password": "password",        // 密码
  "parameters": "charset=utf8mb4&useSSL=false", // 连接参数（可选）
  "connectTimeout": 30,          // 连接超时时间（秒）
  "readTimeout": 60              // 读取超时时间（秒）
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true,
    "responseTime": 156,
    "databaseVersion": "MySQL 8.0.33",
    "availableTables": ["users", "orders", "products"],
    "connectionInfo": "连接成功",
    "totalTableCount": 25
  }
}
```

### 2. 执行数据同步

**接口地址**: `POST /heterogeneous/dataAccess/executeSync`

**功能**: 将源数据库的数据同步到当前MySQL数据库

**请求参数**:
```json
{
  "sourceDatabase": {
    "dbType": 1,
    "host": "*************",
    "port": 3306,
    "database": "source_db",
    "username": "root",
    "password": "password"
  },
  "sourceTable": "users",         // 源表名
  "targetDatabase": "sync_db",    // 目标数据库名（在当前MySQL中创建）
  "targetTable": "users_copy",    // 目标表名
  "overwriteTable": false,        // 是否覆盖已存在的表
  "syncMode": 1,                  // 同步模式：1-全量同步, 2-增量同步
  "batchSize": 1000,              // 批量大小
  "description": "用户表数据同步"  // 同步描述
}
```

**MongoDB特殊配置**:
```json
{
  "sourceDatabase": {
    "dbType": 3,
    "host": "127.0.0.1",
    "port": 27017,
    "database": "mongo_db",
    "username": "admin",
    "password": "password"
  },
  "sourceTable": "users",
  "targetDatabase": "sync_db",
  "targetTable": "mongo_users",
  "mongoConfig": {
    "collection": "users",        // MongoDB集合名
    "queryFilter": "{}",          // 查询条件JSON
    "storageFormat": 1            // 存储格式：1-JSON字符串, 2-展开为字段
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "taskId": "uuid-task-id",
    "status": 1,
    "statusDesc": "准备中",
    "progress": 0,
    "sourceDbType": 1,
    "sourceHost": "*************:3306",
    "sourceTable": "users",
    "targetDatabase": "sync_db",
    "targetTable": "users_copy",
    "startTime": "2025-08-31T10:00:00",
    "description": "用户表数据同步"
  }
}
```

### 3. 查询同步任务状态

**接口地址**: `GET /heterogeneous/dataAccess/syncStatus/{taskId}`

**功能**: 根据任务ID查询数据同步任务的执行状态

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "taskId": "uuid-task-id",
    "status": 3,
    "statusDesc": "同步成功",
    "progress": 100,
    "totalRecords": 10000,
    "syncedRecords": 10000,
    "failedRecords": 0,
    "startTime": "2025-08-31T10:00:00",
    "endTime": "2025-08-31T10:05:30",
    "duration": 330000
  }
}
```

### 4. 分页查询同步的数据

**接口地址**: `GET /heterogeneous/dataAccess/syncedData`

**功能**: 查看已同步到目标表的数据

**请求参数**:
- `targetDatabase`: 目标数据库名
- `targetTable`: 目标表名
- `current`: 当前页（默认1）
- `size`: 每页大小（默认10）

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 100,
    "records": [
      {
        "id": "1",
        "targetData": "{\"id\":1,\"name\":\"张三\",\"age\":25}",
        "targetTable": "users_copy",
        "syncTime": "2025-08-31T10:05:30"
      }
    ]
  }
}
```

### 5. 获取同步任务列表

**接口地址**: `GET /heterogeneous/dataAccess/syncTasks`

**功能**: 分页查询所有数据同步任务

**请求参数**:
- `current`: 当前页（默认1）
- `size`: 每页大小（默认10）

## 使用流程

1. **测试连接**: 首先调用测试连接接口，确保能够正常连接到源数据库
2. **配置同步**: 根据测试结果配置同步参数，选择要同步的表
3. **执行同步**: 调用执行同步接口，获得任务ID
4. **监控进度**: 使用任务ID定期查询同步状态和进度
5. **查看结果**: 同步完成后，可以查询同步的数据

## 注意事项

1. **权限要求**: 确保源数据库用户具有读取权限，当前MySQL用户具有创建数据库和表的权限
2. **网络连通**: 确保应用服务器能够访问源数据库
3. **数据类型映射**: 系统会自动进行数据类型映射，复杂类型可能需要手动处理
4. **大数据量**: 对于大数据量同步，建议适当调整批量大小和超时时间
5. **MongoDB存储**: MongoDB数据默认以JSON格式存储在MySQL的JSON字段中

## 错误处理

- 连接失败: 检查网络连通性和认证信息
- 同步失败: 查看错误信息，可能是权限、数据类型或网络问题
- 进度停滞: 可能是大事务导致，建议减小批量大小
