package com.hzw.heterogeneous.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.request.TextSearchCondition;
import com.hzw.heterogeneous.controller.response.EntitySearchVo;
import com.hzw.heterogeneous.controller.response.TextSearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2024-05-14
 */
public interface TextSearchMapper {

    /**
     * 分页检索文献信息
     * @param page
     * @param condition
     * @return
     */
    IPage<TextSearchVo> literatureListPage(@Param("page") IPage<TextSearchVo> page, @Param("condition") TextSearchCondition condition);

    /**
     * 分页检索新闻信息
     * @param page
     * @param condition
     * @return
     */
    IPage<TextSearchVo> newsListPage(@Param("page") IPage<TextSearchVo> page, @Param("condition") TextSearchCondition condition);

    /**
     * 分页检索新闻信息
     * @param tags
     * @return
     */
    List<EntitySearchVo> queryEntityByTags(@Param("tags") String tags);


    TextSearchVo getNewsById(@Param("id")String id);

    TextSearchVo getLiteratureById(@Param("id")String id);
}
