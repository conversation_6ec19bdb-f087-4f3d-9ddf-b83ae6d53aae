package com.hzw.heterogeneous.service;

import com.hzw.heterogeneous.controller.request.DatabaseConnectionReq;
import com.hzw.heterogeneous.controller.request.DataSyncReq;
import com.hzw.heterogeneous.controller.response.DatabaseConnectionTestVo;
import com.hzw.heterogeneous.controller.response.DataSyncVo;
import com.hzw.heterogeneous.util.Result;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据同步服务测试类
 * <AUTHOR>
 * @date 2025-08-31
 */
@SpringBootTest
@ActiveProfiles("dev")
public class DataAccessServiceTest {

    @Autowired
    private DataAccessService dataAccessService;

    /**
     * 测试MySQL数据库连接
     */
    @Test
    public void testMySQLConnection() {
        DatabaseConnectionReq connectionReq = new DatabaseConnectionReq();
        connectionReq.setDbType(1); // MySQL
        connectionReq.setHost("127.0.0.1");
        connectionReq.setPort(3306);
        connectionReq.setDatabase("test");
        connectionReq.setUsername("root");
        connectionReq.setPassword("password");
        connectionReq.setParameters("useSSL=false&serverTimezone=Asia/Shanghai");

        Result<DatabaseConnectionTestVo> result = dataAccessService.testDatabaseConnection(connectionReq);
        
        assertNotNull(result);
        assertEquals(200, result.getCode());
        
        DatabaseConnectionTestVo testResult = result.getData();
        assertNotNull(testResult);
        
        // 注意：这个测试可能会失败，因为测试环境可能没有对应的数据库
        // 在实际环境中，如果连接成功，success应该为true
        System.out.println("连接测试结果: " + testResult.getSuccess());
        System.out.println("响应时间: " + testResult.getResponseTime() + "ms");
        System.out.println("错误信息: " + testResult.getErrorMessage());
    }

    /**
     * 测试Oracle数据库连接
     */
    @Test
    public void testOracleConnection() {
        DatabaseConnectionReq connectionReq = new DatabaseConnectionReq();
        connectionReq.setDbType(2); // Oracle
        connectionReq.setHost("127.0.0.1");
        connectionReq.setPort(1521);
        connectionReq.setDatabase("XE");
        connectionReq.setUsername("system");
        connectionReq.setPassword("password");

        Result<DatabaseConnectionTestVo> result = dataAccessService.testDatabaseConnection(connectionReq);
        
        assertNotNull(result);
        assertEquals(200, result.getCode());
        
        DatabaseConnectionTestVo testResult = result.getData();
        assertNotNull(testResult);
        
        System.out.println("Oracle连接测试结果: " + testResult.getSuccess());
        System.out.println("响应时间: " + testResult.getResponseTime() + "ms");
        System.out.println("错误信息: " + testResult.getErrorMessage());
    }

    /**
     * 测试MongoDB连接
     */
    @Test
    public void testMongoDBConnection() {
        DatabaseConnectionReq connectionReq = new DatabaseConnectionReq();
        connectionReq.setDbType(3); // MongoDB
        connectionReq.setHost("127.0.0.1");
        connectionReq.setPort(27017);
        connectionReq.setDatabase("test");
        connectionReq.setUsername("admin");
        connectionReq.setPassword("password");

        Result<DatabaseConnectionTestVo> result = dataAccessService.testDatabaseConnection(connectionReq);
        
        assertNotNull(result);
        assertEquals(200, result.getCode());
        
        DatabaseConnectionTestVo testResult = result.getData();
        assertNotNull(testResult);
        
        System.out.println("MongoDB连接测试结果: " + testResult.getSuccess());
        System.out.println("响应时间: " + testResult.getResponseTime() + "ms");
        System.out.println("错误信息: " + testResult.getErrorMessage());
    }

    /**
     * 测试数据同步任务创建
     */
    @Test
    public void testCreateSyncTask() {
        // 创建源数据库连接配置
        DatabaseConnectionReq sourceDb = new DatabaseConnectionReq();
        sourceDb.setDbType(1); // MySQL
        sourceDb.setHost("127.0.0.1");
        sourceDb.setPort(3306);
        sourceDb.setDatabase("source_db");
        sourceDb.setUsername("root");
        sourceDb.setPassword("password");

        // 创建同步请求
        DataSyncReq syncReq = new DataSyncReq();
        syncReq.setSourceDatabase(sourceDb);
        syncReq.setSourceTable("users");
        syncReq.setTargetDatabase("test_sync");
        syncReq.setTargetTable("users_copy");
        syncReq.setOverwriteTable(false);
        syncReq.setSyncMode(1); // 全量同步
        syncReq.setBatchSize(1000);
        syncReq.setDescription("测试用户表同步");

        Result<DataSyncVo> result = dataAccessService.executeDataSync(syncReq);
        
        assertNotNull(result);
        assertEquals(200, result.getCode());
        
        DataSyncVo syncTask = result.getData();
        assertNotNull(syncTask);
        assertNotNull(syncTask.getTaskId());
        assertEquals(1, syncTask.getStatus()); // 准备中
        assertEquals("test_sync", syncTask.getTargetDatabase());
        assertEquals("users_copy", syncTask.getTargetTable());
        
        System.out.println("同步任务ID: " + syncTask.getTaskId());
        System.out.println("任务状态: " + syncTask.getStatusDesc());
        
        // 等待一段时间后查询任务状态
        try {
            Thread.sleep(2000); // 等待2秒
            Result<DataSyncVo> statusResult = dataAccessService.getSyncTaskStatus(syncTask.getTaskId());
            if (statusResult.getData() != null) {
                System.out.println("任务进度: " + statusResult.getData().getProgress() + "%");
                System.out.println("同步状态: " + statusResult.getData().getStatusDesc());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 测试获取同步任务列表
     */
    @Test
    public void testListSyncTasks() {
        Result result = dataAccessService.listSyncTasks(1, 10);
        
        assertNotNull(result);
        assertEquals(200, result.getCode());
        
        System.out.println("同步任务列表查询成功");
    }
}
