<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.heterogeneous.mapper.SearchLogMapper">

    <select id="queryList" resultType="com.hzw.heterogeneous.model.SearchLog">
        SELECT distinct
            t1.key_words
        from
            t_search_log t1
        where
            t1.is_delete = 0
        <if test="keyWords != null and keyWords != ''">
            and t1.key_words like concat('%',#{keyWords},'%')
        </if>
        <if test="pageType != null">
            and t1.page_type = #{pageType}
        </if>
        GROUP BY
            t1.key_words
        ORDER BY
            MAX(t1.created_time) DESC
        limit 10
    </select>
</mapper>