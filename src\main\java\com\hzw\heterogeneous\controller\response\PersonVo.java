package com.hzw.heterogeneous.controller.response;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hzw.heterogeneous.model.PersonTrend;
import com.hzw.heterogeneous.model.Picture;
import com.hzw.heterogeneous.model.Video;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonVo implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 用户姓名
     */
     @Excel(name  = "姓名", orderNum = "0",width = 10)
    private String userName;
    /**
     * 预览路径（保留兼容性）
     */
    private String url;

    /**
     * base64编码（保留兼容性）
     */
    private String base64;
    
    /**
     * 关联的多张图片列表
     */
    private List<Picture> pictures;
    
    /**
     * 关联的多个视频列表
     */
    private List<Video> videos;
    
    /**
     * 视频封面图片base64编码列表
     */
    private List<String> videoThumbnailBase64List;

    /**
     * 国家
     */
     @Excel(name  = "国家", orderNum = "1",width = 20)
    private String country;

    /**
     * 职务
     */
     @Excel(name  = "职务", orderNum = "2",width = 10)
    private String position;

    /**
     * 任职任期
     */
     @Excel(name  = "任职任期", orderNum = "3",width = 20)
    private String term;

    /**
     * 毕业院校
     */
     @Excel(name  = "毕业院校", orderNum = "4",width = 20)
    private String graduateSchool;

    /**
     * 类型
     */
     @Excel(name  = "类型", orderNum = "5",width = 10)
    private String type;

    /**
     * 履历
     */
     @Excel(name  = "履历", orderNum = "6",width = 30)
    private String resume;

    /**
     * 关键词标签
     */
    @Excel(name  = "关键词标签", orderNum = "7",width = 25)
    private String keyTags;

    /**
     * 工作经历
     */
    @Excel(name  = "工作经历", orderNum = "8",width = 30)
    private String workExp;

    private List<PersonTrend> personTrends;
}
