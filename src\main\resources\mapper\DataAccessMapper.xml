<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.heterogeneous.mapper.DataAccessMapper">

    <!-- 分页检索文献信息 -->
    <select id="listPage" resultType="com.hzw.heterogeneous.controller.response.DataAccessVo">
        SELECT
            id,
            file_name as fileName,
            file_url as fileUrl,
            file_type as fileType,
            file_format as fileFormat,
            date_format(created_time,'%Y-%m-%d %H:%i:%s') as createdTime
        FROM t_data_access
        where is_delete = 0
        <if test="null != condition.keyWords and '' != condition.keyWords ">
            and MATCH(file_name,file_type,file_format) AGAINST (#{condition.keyWords} IN BOOLEAN MODE)
        </if>
        <if test="null != condition.type and '' != condition.type ">
            and data_type = #{condition.type}
        </if>
        ORDER BY created_time DESC
    </select>

</mapper>
