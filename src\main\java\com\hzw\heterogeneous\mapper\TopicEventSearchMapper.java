package com.hzw.heterogeneous.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzw.heterogeneous.controller.response.TopicEventSearchVo;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import com.hzw.heterogeneous.model.TopicEventSearch;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TopicEventSearchMapper extends BaseMapper<TopicEventSearch> {
    /**
     * 主题事件列表
     * @param buildPage
     * @param condition
     * @return
     */
    IPage<TopicEventSearchVo> listPage(@Param("buildPage") IPage<TopicEventSearchVo> buildPage, @Param("condition")ThematicAnalysisCondition condition);

    /**
     * 查询关联数量
     * @param words
     * @return
     */
    Integer getCount(@Param("words") String words);
}
