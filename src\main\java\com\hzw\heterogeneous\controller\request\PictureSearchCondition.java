package com.hzw.heterogeneous.controller.request;

import com.hzw.heterogeneous.util.BaseCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PictureSearchCondition extends BaseCondition {

    @ApiModelProperty(value = "查询条件")
    private String keyWords;

    @ApiModelProperty(value = "图片类型1人物2武器")
    private Integer picType;

    @ApiModelProperty(value = "排序方式  1 相关度  2 时间")
    private Integer orderType;
}
