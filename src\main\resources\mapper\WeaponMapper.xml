<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.heterogeneous.mapper.WeaponMapper">

    <resultMap id="weaponResultMap" type="com.hzw.heterogeneous.controller.response.WeaponVo">
        <id property="id" column="id"/>
        <result property="weaponName" column="name"/>
        <result property="weaponNameEng" column="name_eng"/>
        <result property="code" column="code"/>
        <result property="country" column="country"/>
        <result property="param" column="param"/>
        <result property="type" column="type"/>
        <result property="tags" column="tags"/>
        <result property="keyTags" column="key_tags"/>
    </resultMap>

    <select id="listWeaponNum" resultType="com.hzw.heterogeneous.controller.response.EntityTypeNumVo">
        SELECT '导弹' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '导弹'
        UNION ALL
        SELECT '电子战' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '电子战'
        UNION ALL
        SELECT '飞机' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '飞机'
        UNION ALL
        SELECT '光电/红外' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '光电/红外'
        UNION ALL
        SELECT '火箭/鱼雷' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '火箭/鱼雷'
        UNION ALL
        SELECT '舰艇' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '舰艇'
        UNION ALL
        SELECT '雷达' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '雷达'
        UNION ALL
        SELECT '模拟与训练系统' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '模拟与训练系统'
        UNION ALL
        SELECT '枪炮' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '枪炮'
        UNION ALL
        SELECT '通信' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '通信'
        UNION ALL
        SELECT '炸弹' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '炸弹'
        UNION ALL
        SELECT '指挥与控制' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '指挥与控制'
        UNION ALL
        SELECT '装甲车辆' AS entityType, COUNT(*) AS num FROM t_weapon WHERE type = '装甲车辆'
    </select>
    <select id="listWeaponPage" resultType="com.hzw.heterogeneous.controller.response.WeaponVo">
        SELECT
            id,
            name AS weaponName,
            name_eng AS weaponNameEng,
            code,
            country,
            param,
            type,
            tags,
            key_tags AS keyTags
        FROM
            t_weapon
        WHERE
            1 = 1
        <if test="condition.keyWords != null and condition.keyWords != ''">
            AND (
                name LIKE CONCAT('%', #{condition.keyWords}, '%')
                OR name_eng LIKE CONCAT('%', #{condition.keyWords}, '%')
            )
        </if>
        <if test="condition.type != null and condition.type != ''">
            AND type = #{condition.type}
        </if>
        ORDER BY id DESC
    </select>
    <select id="getWeaponInfo" resultMap="weaponResultMap">
        SELECT
            id,
            name,
            name_eng,
            code,
            country,
            param,
            type,
            tags,
            key_tags
        FROM
            t_weapon
        WHERE id = #{id}
    </select>

    <!-- WeaponMapper.xml -->
    <select id="selectByPaths" resultType="com.hzw.heterogeneous.model.Weapon">
        SELECT
        w.id,
        p.url as photo,
        w.name,
        p.tags,
        w.created_time
        FROM
        t_weapon w
        INNER JOIN t_person_weapon_picture_rel rel ON w.id = rel.person_weapon_id AND rel.picture_type = '2'
        INNER JOIN t_picture p ON p.id = rel.picture_id
        WHERE
        p.url IN
        <foreach item="path" collection="paths" open="(" separator="," close=")">
            #{path}
        </foreach>
        AND w.id IS NOT NULL
    </select>

    <select id="searchWeaponsByKeyword" resultType="com.hzw.heterogeneous.model.Weapon">
        SELECT
            id,
            name,
            name_eng as nameEng
        FROM
            t_weapon
        WHERE
            1 = 1
            AND (
                name LIKE CONCAT('%', #{keyword}, '%')
                OR name_eng LIKE CONCAT('%', #{keyword}, '%')
            )
        ORDER BY id DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

</mapper>