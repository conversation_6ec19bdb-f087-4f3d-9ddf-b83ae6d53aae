package com.hzw.heterogeneous.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.response.TopicEventSearchVo;
import com.hzw.heterogeneous.mapper.EventsMapper;
import com.hzw.heterogeneous.model.Events;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import com.hzw.heterogeneous.dto.TopicEventSearchDTO;
import com.hzw.heterogeneous.mapper.TopicEventSearchMapper;
import com.hzw.heterogeneous.model.TopicEventSearch;
import com.hzw.heterogeneous.service.TopicEventSearchService;
import com.hzw.heterogeneous.util.Paging;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class TopicEventSearchServiceImpl implements TopicEventSearchService {

    @Autowired
    private TopicEventSearchMapper topicEventSearchMapper;

    @Autowired
    private EventsMapper eventsMapper;

    /**
     * 分页查询主题事件
     * @param condition
     * @return
     */
    @Override
    public Paging<TopicEventSearchVo> listPage(ThematicAnalysisCondition condition) {
        IPage<TopicEventSearchVo> page = condition.buildPage();
        if(StringUtils.isNotBlank(condition.getTopicEvent())){
            condition.setWords(condition.getTopicEvent().replace("、"," "));
        }
        page = topicEventSearchMapper.listPage(page, condition);
        return Paging.buildPaging(page);
    }

    /**
     * 查询主题事件列表
     * @param eventSearch
     * @return
     */
    @Override
    public List<TopicEventSearchDTO> listTopicEvent(TopicEventSearch eventSearch) {
        List<TopicEventSearchDTO> dtos = new ArrayList<>();
        LambdaQueryWrapper<TopicEventSearch> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TopicEventSearch::getIsDelete,0)
                        .orderByAsc(TopicEventSearch::getCreatedTime);
        List<TopicEventSearch> topicEventSearches = topicEventSearchMapper.selectList(queryWrapper);
        for (TopicEventSearch topicEventSearch : topicEventSearches) {
            TopicEventSearchDTO dto = new TopicEventSearchDTO();
            BeanUtils.copyProperties(topicEventSearch,dto);
            //List<String> words = Arrays.asList(dto.getKeywords().split(","));
            String words = dto.getKeywords().replace("、"," ");
            dto.setNum(topicEventSearchMapper.getCount(words));
            dtos.add(dto);
        }
        return dtos;
    }

    /**
     * 新增主题事件列表
     * @param eventSearch
     * @return
     */
    @Override
    public Boolean addTopicEvent(TopicEventSearch eventSearch) {
        eventSearch.setEventName(eventSearch.getEventName().trim());
        eventSearch.setKeywords(eventSearch.getKeywords().trim());
        eventSearch.setCreatedTime(new Date());
        eventSearch.setUpdatedTime(new Date());
        return topicEventSearchMapper.insert(eventSearch) > 0;
    }

    /**
     * 删除主题事件列表
     * @param eventSearch
     * @return
     */
    @Override
    public Boolean removeTopicEvent(TopicEventSearch eventSearch) {
        eventSearch.setIsDelete(1);
        eventSearch.setUpdatedTime(new Date());
        return topicEventSearchMapper.updateById(eventSearch) > 0;
    }

    /**
     * 导出数据
     * @param ids
     * @return
     */
    @Override
    public List<TopicEventSearchVo> exportList(List<String> ids) {
        LambdaQueryWrapper<Events> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Events::getId,ids);
//        queryWrapper.eq(Events::getIsDelete,0);
        List<Events> events = eventsMapper.selectList(queryWrapper);
        List<TopicEventSearchVo> topicEventSearchVos = new ArrayList<>();
        for (Events topicEventSearch : events) {
            TopicEventSearchVo topicEventSearchVo = new TopicEventSearchVo();
            BeanUtils.copyProperties(topicEventSearch,topicEventSearchVo);
            topicEventSearchVos.add(topicEventSearchVo);
        }
        return topicEventSearchVos;
    }

    @Override
    public TopicEventSearchVo getTopicEventInfo(String id) {
        Events events = eventsMapper.selectById(id);
        return BeanUtil.copyProperties(events, TopicEventSearchVo.class);
    }
}
