package com.hzw.heterogeneous.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzw.heterogeneous.constant.CommonConstants;
import com.hzw.heterogeneous.controller.request.PictureSearchCondition;
import com.hzw.heterogeneous.controller.response.PictureSearchVo;
import com.hzw.heterogeneous.dto.PicSearchResut;
import com.hzw.heterogeneous.mapper.PersonMapper;
import com.hzw.heterogeneous.mapper.PictureSearchMapper;
import com.hzw.heterogeneous.mapper.WeaponMapper;
import com.hzw.heterogeneous.model.Person;
import com.hzw.heterogeneous.model.Weapon;
import com.hzw.heterogeneous.service.PictureSearchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2024-05-14
 */
@Slf4j
@Service
public class PictureSearchServiceImpl implements PictureSearchService {

    @Resource
    private PictureSearchMapper pictureSearchMapper;

    @Resource
    private PersonMapper personMapper;
    @Resource
    private WeaponMapper weaponMapper;

    // 1. 使用@Value从配置文件读取URL
    @Value("${api.image.match.url}")
    private String externalApiUrl;

    @Value("${api.image.match.tmpDirPath}")
    private String tmpDirPath;
    @Value("${api.image.match.cleanup.enable}")
    private Boolean cleanupEnable;


    @Override
    public IPage<PictureSearchVo> listPage(PictureSearchCondition condition) {
        IPage<PictureSearchVo> page = condition.buildPage();
        if(CommonConstants.PERSON.equals(condition.getPicType())){
            page = pictureSearchMapper.listPicturePageByPerson(page, condition);
        } else if(CommonConstants.WEAPON.equals(condition.getPicType())) {
            page = pictureSearchMapper.listPicturePageByWeapon(page, condition);
        }
        if(CollectionUtils.isNotEmpty(page.getRecords())){
            for(PictureSearchVo vo:page.getRecords()){
                String base64 = getBase64(vo.getUrl());
                vo.setBase64(base64);
            }
        }
        return page;
    }

//    public String getBase64(String url){
//        // 获取图片的路径或ID
//        if (StringUtils.isBlank(url)) {
//            return null;
//        }
//
//        File imageFile = new File(url);
//        if (!imageFile.exists()) {
//            return null;
//        }
//        String base64Image = null;
//        // 读取图片文件
//        FileInputStream fis = null;
//        try{
//            fis = new FileInputStream(imageFile);
//            byte[] imageBytes = new byte[(int) imageFile.length()];
//            fis.read(imageBytes);
//            // 将字节数组转换为Base64字符串
//            base64Image = Base64.getEncoder().encodeToString(imageBytes);
//        } catch (Exception e) {
//            log.error("图片转base64失败", e);
//        }finally {
//            if (fis != null) {
//                try {
//                    fis.close();
//                } catch (IOException e) {
//                    log.error("关闭文件流失败", e);
//                }
//            }
//        }
//        return base64Image;
//    }
    public String getBase64(String url) {
        if (StringUtils.isBlank(url)) {
            log.warn("传入的图片路径为空");
            return null;
        }

        File imageFile = new File(url);
        if (!imageFile.exists()) {
            log.warn("图片文件不存在: {}", imageFile.getAbsolutePath());
            return null;
        }

        try (FileInputStream fis = new FileInputStream(imageFile)) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) > 0) {
                baos.write(buffer, 0, len);
            }
            byte[] imageBytes = baos.toByteArray();
            return Base64.getEncoder().encodeToString(imageBytes);
        } catch (IOException e) {
            log.error("图片转base64失败，文件路径：{}", imageFile.getAbsolutePath(), e);
            return null;
        }
    }

//    public IPage<PictureSearchVo> uploadImageAndCallApi(MultipartFile file) {
//        // 从配置文件读取API地址和临时目录路径
////        String externalApiUrl = "http://***************:28085/api/image_match"; // 实际应从配置读取
////        String tmpDirPath = "/Users/<USER>/temporary/upload"; // 实际应从配置读取
//
//        // 确保临时目录存在
//        File tmpDir = new File(tmpDirPath);
//        if (!tmpDir.exists() && !tmpDir.mkdirs()) {
//            throw new RuntimeException("临时目录创建失败");
//        }
//
//        // 生成文件名（处理无扩展名情况）
//        String originalFilename = file.getOriginalFilename();
//        String extension = "";
//        if (originalFilename != null) {
//            int lastDotIndex = originalFilename.lastIndexOf(".");
//            extension = (lastDotIndex != -1) ? originalFilename.substring(lastDotIndex) : ".unknown";
//        }
//        String fileName = UUID.randomUUID() + extension;
//        String filePath = tmpDirPath + File.separator + fileName;
//
//        File tempFile = new File(filePath);
//        try {
//            // 保存文件
//            file.transferTo(tempFile);
//
//            // 构造API请求路径（使用Path处理）
//            Path imagePath = Paths.get(tmpDirPath, fileName);
//            String imageUri = imagePath.toString().replaceFirst("^/", ""); // 去除开头斜杠
//
//            Map<String, String> requestBody = new HashMap<>();
//            requestBody.put("image_path", imageUri);
//
//            // 发送请求
//            RestTemplate restTemplate = new RestTemplate();
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
//
//            ResponseEntity<String> response = restTemplate.postForEntity(
//                    externalApiUrl,
//                    requestEntity,
//                    String.class
//            );
//            if (!response.getStatusCode().is2xxSuccessful()) {
//                throw new RuntimeException("接口调用失败，状态码：" + response.getStatusCode());
//            }
//
//            // 解析响应
//            List<PicSearchResut> results;
//            try {
//                results = JSONUtil.toList(response.getBody(), PicSearchResut.class);
//            } catch (Exception e) {
//                throw new RuntimeException("响应解析失败", e);
//            }
//
//// 批量查询与缓存
//            List<String> personPaths = new ArrayList<>();
//            List<String> weaponPaths = new ArrayList<>();
//            for (PicSearchResut result : results) {
//                String path = result.getPath();
//                if (path.contains("/人物/")) {
//                    personPaths.add(path);
//                } else if (path.contains("/武器/")) {
//                    weaponPaths.add(path);
//                } else {
//                    log.warn("未知路径类型，路径：{}", path);
//                }
//            }
//
//// 分表查询
//            List<Person> personInfos = personMapper.selectByPaths(personPaths);
//            List<Weapon> weaponInfos = weaponMapper.selectByPaths(weaponPaths); // 需确保存在WeaponMapper和Weapon类
//
//// 构建缓存（分开存储）
//            Map<String, Person> personCache = personInfos.stream()
//                    .collect(Collectors.toMap(Person::getPhoto, pi -> pi));
//
//            Map<String, Weapon> weaponCache = weaponInfos.stream()
//                    .collect(Collectors.toMap(Weapon::getPhoto, wi -> wi));
//
//// 转换为VO时根据类型取数据
//            List<PictureSearchVo> voList = new ArrayList<>();
//            for (PicSearchResut result : results) {
//                PictureSearchVo vo = new PictureSearchVo();
//                String path = result.getPath();
//
//                if (path.contains("/人物/")) {
//                    Person person = personCache.get(path);
//                    if (person != null) {
//                        vo.setId(person.getId());
//                        vo.setName(person.getUserName());
//                        vo.setTags(person.getTags());
//                        vo.setCreatedTime(DateUtil.format(person.getCreatedTime(), "yyyy-MM-dd HH:mm:ss"));
//                    } else {
//                        vo.setName("");
//                        vo.setTags("");
//                        vo.setCreatedTime("-");
//                    }
//                } else if (path.contains("/武器/")) {
//                    Weapon weapon = weaponCache.get(path);
//                    if (weapon != null) {
//                        vo.setId(weapon.getId()); // 假设Weapon类有对应字段
//                        vo.setName(weapon.getName()); // 假设Weapon类有对应字段
//                        vo.setTags(weapon.getTags()); // 假设Weapon类有对应字段
//                        vo.setCreatedTime(DateUtil.format(weapon.getCreatedTime(), "yyyy-MM-dd HH:mm:ss"));
//                    } else {
//                        vo.setName("");
//                        vo.setTags("");
//                        vo.setCreatedTime("-");
//                    }
//                } else {
//                    // 处理未知类型路径的默认值
//                    vo.setName("");
//                    vo.setTags("");
//                    vo.setCreatedTime("-");
//                }
//
//                // 原有字段映射
//                vo.setUrl(result.getPath());
//                vo.setScore(result.getScore());
//                vo.setBase64(getBase64(result.getPath()));
//
//                voList.add(vo);
//            }
//
//            // 封装分页
//            IPage<PictureSearchVo> page = new Page<>(1, results.size());
//            page.setRecords(voList);
//            page.setTotal(results.size());
//            return page;
//        } catch (IOException e) {
//            log.error("文件处理失败", e);
//            throw new RuntimeException("文件处理失败", e);
//        } finally {
//            // 删除临时文件
//            if (tempFile.exists()) {
//                if (!tempFile.delete()) {
//                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
//                }
//            }
//        }
//    }
public IPage<PictureSearchVo> uploadImageAndCallApi(MultipartFile file) {
    // 确保临时目录存在
    File tmpDir = new File(tmpDirPath);
    if (!tmpDir.exists() && !tmpDir.mkdirs()) {
        throw new RuntimeException("临时目录创建失败: " + tmpDir.getAbsolutePath());
    }

    // 生成文件名（处理无扩展名情况）
    String originalFilename = file.getOriginalFilename();
    String extension = ".unknown";
    if (originalFilename != null) {
        int lastDotIndex = originalFilename.lastIndexOf(".");
        extension = (lastDotIndex != -1) ? originalFilename.substring(lastDotIndex) : extension;
    }
    String fileName = UUID.randomUUID() + extension;
    File tempFile = new File(tmpDir, fileName); // 安全构建路径

    try {
        // 保存文件
        file.transferTo(tempFile);
        log.info("文件保存成功，路径：{}", tempFile.getAbsolutePath()); // 1. 文件保存成功日志
        // 构造API请求路径
//        Path imagePath = tempFile.toPath();
//        String imageUri = imagePath.toString().replaceFirst("^/", "");
        String imageUri = tempFile.getAbsolutePath();
        log.info("构造API请求参数，imageUri:{}", imageUri); // 2. 请求参数日志

//        imageUri="/sunflower/temporary/home/<USER>/tmpDirPath/2997938038.png";
//        imageUri="/sunflower/temporary/home/<USER>/tmpDirPath/5803512071.jpg";
//        imageUri="/sunflower/temporary/home/<USER>/image/武器/1002186592/GZ7qSrDXwAA7DA9.jpg";
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("image_path", imageUri);

            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            log.debug("发送API请求，URL：{}，请求体：{}", externalApiUrl, requestBody);
            ResponseEntity<String> response = restTemplate.postForEntity(
                    externalApiUrl,
                    requestEntity,
                    String.class
            );
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("API调用失败，状态码：{}，响应内容：{}", response.getStatusCode(), response.getBody()); // 4. 失败响应日志
                throw new RuntimeException("接口调用失败，状态码：" + response.getStatusCode());
            }

            // 解析响应
            List<PicSearchResut> results;
            try {
                results = JSONUtil.toList(response.getBody(), PicSearchResut.class);
                log.info("API返回结果解析成功，共{}条数据", results.size()); // 5. 响应解析日志
            } catch (Exception e) {
                log.error("响应解析异常", e); // 6. 解析异常日志
                throw new RuntimeException("响应解析失败", e);
            }

        // 分表查询优化空列表处理
//        List<String> personPaths = filterPaths(results, "/人/");
//        List<String> weaponPaths = filterPaths(results, "/武器/");
//        log.debug("分表查询条件：人物路径{}条，武器路径{}条", personPaths.size(), weaponPaths.size()); // 7. 查询条件日志
//        List<Person> personInfos = personPaths.isEmpty() ? Collections.emptyList()
//                : personMapper.selectByPaths(personPaths);
//        List<Weapon> weaponInfos = weaponPaths.isEmpty() ? Collections.emptyList()
//                : weaponMapper.selectByPaths(weaponPaths);
        List<String> paths = filterPaths(results);
        log.debug("分表查询条件：路径{}条", paths.size()); // 7. 查询条件日志
        List<Person> personInfos = paths.isEmpty() ? Collections.emptyList()
                : personMapper.selectByPaths(paths);
        List<Weapon> weaponInfos = paths.isEmpty() ? Collections.emptyList()
                : weaponMapper.selectByPaths(paths);
        log.info("数据库查询结果：人物{}条，武器{}条", personInfos.size(), weaponInfos.size()); // 8. 查询结果日志
        // 将personInfos和weaponInfos 转换成List<PictureSearchVo>
        List<PictureSearchVo> voList=new ArrayList<>();

        personInfos.forEach(person->{
            PictureSearchVo vo = new PictureSearchVo();
            vo.setId(person.getId());
            vo.setName(person.getUserName());
            vo.setTags(person.getTags());
            vo.setCreatedTime(DateUtil.format(person.getCreatedTime(), "yyyy-MM-dd HH:mm:ss"));
            vo.setType(CommonConstants.PERSON);
            voList.add(vo);
        });
        weaponInfos.forEach(weapon->{
            PictureSearchVo vo = new PictureSearchVo();
            vo.setId(weapon.getId());
            vo.setName(weapon.getName());
            vo.setTags(weapon.getTags());
            vo.setCreatedTime(DateUtil.format(weapon.getCreatedTime(), "yyyy-MM-dd HH:mm:ss"));
            vo.setType(CommonConstants.WEAPON);
            voList.add(vo);
        });





//        List<PictureSearchVo> voList

        // 构建缓存
//        Map<String, List<Person>> personCache = toMultiMap(personInfos, Person::getPhoto);
//        Map<String, List<Weapon>> weaponCache = toMultiMap(weaponInfos, Weapon::getPhoto);
//        log.debug("缓存构建完成：人物缓存{}条，武器缓存{}条", personCache.size(), weaponCache.size()); // 9. 缓存构建日志
        // 转换为VO时统一处理默认值
//        List<PictureSearchVo> voList = results.stream()
//                .map(result -> buildVo(result, personCache, weaponCache))
//                .collect(Collectors.toList());


        log.info("VO转换完成，共{}条结果", voList.size()); // 10. VO转换日志
        // 封装分页（假设pageNum为1，pageSize为results.size()）
        IPage<PictureSearchVo> page = new Page<>(1, results.size());
        page.setRecords(voList);
        page.setTotal(results.size());
        log.info("分页封装完成，总页数{}，当前页数据{}条", page.getPages(), page.getSize()); // 11. 分页日志
        return page;
    } catch (IOException e) {
        log.error("文件处理失败：{}", tempFile.getAbsolutePath(), e);
        throw new RuntimeException("文件处理失败", e);
    } finally {
        if (!cleanupEnable){
            if (tempFile.exists()) {
                if (!tempFile.delete()) {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                }
            }
        }

    }
}

    // 辅助方法
    private List<String> filterPaths(List<PicSearchResut> results, String type) {
        return results.stream()
                .map(PicSearchResut::getPath)
                .filter(path -> path.contains(type))
                .collect(Collectors.toList());
    }
    private List<String> filterPaths(List<PicSearchResut> results) {
        return results.stream()
                .map(PicSearchResut::getPath)
                .collect(Collectors.toList());
    }
//    private <T> Map<String, T> toMap(List<T> list, Function<T, String> keyExtractor) {
//        return list.stream()
//                .collect(Collectors.toMap(keyExtractor, Function.identity()));
//    }
    private <T> Map<String, List<T>> toMultiMap(List<T> list, Function<T, String> keyExtractor) {
        return list.stream()
                .collect(Collectors.groupingBy(keyExtractor));
    }
//    private PictureSearchVo buildVo(PicSearchResut result, Map<String, Person> personCache, Map<String, Weapon> weaponCache) {
//        PictureSearchVo vo = new PictureSearchVo();
//        String path = result.getPath();
//
//
//        if (path.contains("/人/")) {
//            Person person = personCache.get(path);
//            setVoFields(vo, person,
//                    Person::getUserName,
//                    Person::getTags,
//                    Person::getCreatedTime,
//                    Person::getId);
//            vo.setType(CommonConstants.PERSON);
//        } else if (path.contains("/武器/")) {
//            Weapon weapon = weaponCache.get(path);
//            setVoFields(vo, weapon,
//                    Weapon::getName,
//                    Weapon::getTags,
//                    Weapon::getCreatedTime,
//                    Weapon::getId);
//            vo.setType(CommonConstants.WEAPON);
//        } else {
//            setDefaultVoFields(vo);
//        }
//
//        vo.setUrl(result.getPath());
//        vo.setScore(result.getScore());
//        vo.setBase64(getBase64(result.getPath()));
//        return vo;
//    }
    private PictureSearchVo buildVo(PicSearchResut result,
                                    Map<String, List<Person>> personCache,
                                    Map<String, List<Weapon>> weaponCache) {
        PictureSearchVo vo = new PictureSearchVo();
        String path = result.getPath();

        if (path.contains("/人/")) {
            List<Person> persons = personCache.get(path);
            Person person = persons != null && !persons.isEmpty() ? persons.get(0) : null;
            setVoFields(vo, person,
                    Person::getUserName,
                    Person::getTags,
                    Person::getCreatedTime,
                    Person::getId);
            vo.setType(CommonConstants.PERSON);
        } else if (path.contains("/武器/")) {
            List<Weapon> weapons = weaponCache.get(path);
            Weapon weapon = weapons != null && !weapons.isEmpty() ? weapons.get(0) : null;
            setVoFields(vo, weapon,
                    Weapon::getName,
                    Weapon::getTags,
                    Weapon::getCreatedTime,
                    Weapon::getId);
            vo.setType(CommonConstants.WEAPON);
        } else {
            setDefaultVoFields(vo);
        }

        vo.setUrl(result.getPath());
        vo.setScore(result.getScore());
        vo.setBase64(getBase64(result.getPath()));
        return vo;
    }


    private <T> void setVoFields(PictureSearchVo vo, T entity,
                                 Function<T, String> nameFunc, Function<T, String> tagsFunc,
                                 Function<T, Date> timeFunc, Function<T, String> idFunc) {
        if (entity != null) {
            vo.setId(idFunc.apply(entity));
            vo.setName(nameFunc.apply(entity));
            vo.setTags(tagsFunc.apply(entity));
            vo.setCreatedTime(DateUtil.format(timeFunc.apply(entity), "yyyy-MM-dd HH:mm:ss"));
        } else {
            setDefaultVoFields(vo);
        }
    }

    private void setDefaultVoFields(PictureSearchVo vo) {
        vo.setName("");
        vo.setTags("");
        vo.setCreatedTime("-");
    }



}
