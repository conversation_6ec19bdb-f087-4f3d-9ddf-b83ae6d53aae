package com.hzw.heterogeneous.service;

import com.hzw.heterogeneous.controller.response.ComprehensiveSearchVo;
import com.hzw.heterogeneous.model.SearchLog;

import java.util.List;

/**
* SearchLogService接口
*
*/
public interface SearchLogService {

    Boolean saveSearchLog(SearchLog searchLog);

    List<SearchLog> listSearchLog(String keyWords, Integer pageType);
    
    /**
     * 综合搜索，包括搜索日志、人物、武器
     * @param keyword 搜索关键词
     * @param pageType 页面类型
     * @param limit 结果数量限制
     * @return 综合搜索结果
     */
    ComprehensiveSearchVo comprehensiveSearch(String keyword, Integer pageType, Integer limit);
}