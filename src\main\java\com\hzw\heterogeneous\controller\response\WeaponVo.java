package com.hzw.heterogeneous.controller.response;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.hzw.heterogeneous.model.WeaponTrend;
import com.hzw.heterogeneous.model.Picture;
import com.hzw.heterogeneous.model.Video;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeaponVo implements Serializable {

    private String id;

    /**
     * 武器名称
     */
    @Excel(name  =  "名称", orderNum = "0",width = 15)
    private String weaponName;

    /**
     * 武器英文名称
     */
    @Excel(name  =  "英文名称", orderNum = "1",width = 15)
    private String weaponNameEng;

    /**
     * 预览路径
     */
    private String url;

    private String base64;

    /**
     * 代号
     */
    @Excel(name  =  "代号", orderNum = "2",width = 10)
    private String code;

    /**
     * 国家
     */
    @Excel(name  =  "国家", orderNum = "3",width = 20)
    private String country;

    /**
     * 参数
     */
    @Excel(name  =  "参数", orderNum = "4",width = 10)
    private String param;

    /**
     * 类型
     */
    @Excel(name  =  "类型", orderNum = "5",width = 10)
    private String type;

    /**
     * 标签
     */
    @Excel(name  =  "标签", orderNum = "6",width = 20)
    private String tags;

    /**
     * 关键词标签
     */
    @Excel(name  =  "关键词标签", orderNum = "7",width = 25)
    private String keyTags;

    /**
     * 相关图片列表
     */
    private List<Picture> pictures;
    
    /**
     * 相关视频列表
     */
    private List<Video> videos;

    /**
     * 目标动向
     */
    private List<WeaponTrend> weaponTrends;
}
