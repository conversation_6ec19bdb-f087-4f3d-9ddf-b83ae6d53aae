package com.hzw.heterogeneous.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.request.DataAccessCondition;
import com.hzw.heterogeneous.controller.request.DataAccessReq;
import com.hzw.heterogeneous.controller.request.DatabaseConnectionReq;
import com.hzw.heterogeneous.controller.request.DataSyncReq;
import com.hzw.heterogeneous.controller.response.DataAccessVo;
import com.hzw.heterogeneous.controller.response.DataSyncVo;
import com.hzw.heterogeneous.controller.response.DatabaseConnectionTestVo;
import com.hzw.heterogeneous.controller.response.SyncedDataVo;

import com.hzw.heterogeneous.service.DataAccessService;
import com.hzw.heterogeneous.util.Paging;
import com.hzw.heterogeneous.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @datetime 2024/11/11 9:06
 * @description: 数据接入服务
 * @version: 1.0
 */
@Api(tags = "数据接入")
@RestController
@RequestMapping("/dataAccess")
public class DataAccessController {

    @Autowired
    private DataAccessService dataAccessService;

    @ApiOperation(value = "分页查询数据接入")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "CurrencyCondition", paramType = "body")
    @PostMapping(value = "/listPage")
    public Result<Paging<DataAccessVo>> listPage(@RequestBody DataAccessCondition condition) {
        IPage<DataAccessVo> page = dataAccessService.listPage(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "删除数据接入")
    @ApiImplicitParam(name = "id", value = "查询条件", required = true, dataType = "id", paramType = "body")
    @GetMapping(value = "/delete/{id}")
    public Result<Boolean> removeById(@PathVariable("id") String id) {
        boolean flag = dataAccessService.removeById(id);
        if(flag){
            return Result.ok();
        }
        return Result.failed();
    }

    @ApiOperation(value = "新增数据接入")
    @ApiImplicitParam(name = "id", value = "查询条件", required = true, dataType = "id", paramType = "body")
    @PostMapping(value = "/addDataAccess")
    public Result<Boolean> addDataAccess(@RequestParam("file") MultipartFile file,
                                         @RequestParam("fileName") String fileName,
                                         @RequestParam("fileType") String fileType,
                                         @RequestParam("fileFormat") String fileFormat,
                                         @RequestParam("dataType") Integer dataType) {
        return dataAccessService.addDataAccess(file,fileName,fileType,fileFormat,dataType);
    }

    // ==================== 数据同步相关接口 ====================

    @ApiOperation(value = "测试数据库连接", notes = "测试源数据库连接是否正常，并返回可用表列表")
    @PostMapping(value = "/testConnection")
    public Result<DatabaseConnectionTestVo> testDatabaseConnection(@Valid @RequestBody DatabaseConnectionReq connectionReq) {
        return dataAccessService.testDatabaseConnection(connectionReq);
    }

    @ApiOperation(value = "执行数据同步", notes = "将源数据库的数据同步到当前MySQL数据库")
    @PostMapping(value = "/executeSync")
    public Result<DataSyncVo> executeDataSync(@Valid @RequestBody DataSyncReq syncReq) {
        return dataAccessService.executeDataSync(syncReq);
    }

    @ApiOperation(value = "查询同步任务状态", notes = "根据任务ID查询数据同步任务的执行状态")
    @GetMapping(value = "/syncStatus/{taskId}")
    public Result<DataSyncVo> getSyncTaskStatus(@PathVariable("taskId") String taskId) {
        return dataAccessService.getSyncTaskStatus(taskId);
    }

    @ApiOperation(value = "分页查询同步的数据", notes = "查看已同步到目标表的数据")
    @GetMapping(value = "/syncedData")
    public Result<Paging<SyncedDataVo>> listSyncedData(
            @RequestParam("targetDatabase") String targetDatabase,
            @RequestParam("targetTable") String targetTable,
            @RequestParam(value = "current", defaultValue = "1") Integer current,
            @RequestParam(value = "size", defaultValue = "10") Integer size) {
        return dataAccessService.listSyncedData(targetDatabase, targetTable, current, size);
    }

    @ApiOperation(value = "获取同步任务列表", notes = "分页查询所有数据同步任务")
    @GetMapping(value = "/syncTasks")
    public Result<Paging<DataSyncVo>> listSyncTasks(
            @RequestParam(value = "current", defaultValue = "1") Integer current,
            @RequestParam(value = "size", defaultValue = "10") Integer size) {
        return dataAccessService.listSyncTasks(current, size);
    }

}
