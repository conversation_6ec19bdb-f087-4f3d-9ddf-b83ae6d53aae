package com.hzw.heterogeneous.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.request.DataAccessCondition;
import com.hzw.heterogeneous.controller.request.DataAccessReq;
import com.hzw.heterogeneous.controller.response.DataAccessVo;
import com.hzw.heterogeneous.service.DataAccessService;
import com.hzw.heterogeneous.util.Paging;
import com.hzw.heterogeneous.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @datetime 2024/11/11 9:06
 * @description: 数据接入服务
 * @version: 1.0
 */
@Api(tags = "数据接入")
@RestController
@RequestMapping("/dataAccess")
public class DataAccessController {

    @Autowired
    private DataAccessService dataAccessService;

    @ApiOperation(value = "分页查询数据接入")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "CurrencyCondition", paramType = "body")
    @PostMapping(value = "/listPage")
    public Result<Paging<DataAccessVo>> listPage(@RequestBody DataAccessCondition condition) {
        IPage<DataAccessVo> page = dataAccessService.listPage(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "删除数据接入")
    @ApiImplicitParam(name = "id", value = "查询条件", required = true, dataType = "id", paramType = "body")
    @GetMapping(value = "/delete/{id}")
    public Result<Boolean> removeById(@PathVariable("id") String id) {
        boolean flag = dataAccessService.removeById(id);
        if(flag){
            return Result.ok();
        }
        return Result.failed();
    }

    @ApiOperation(value = "新增数据接入")
    @ApiImplicitParam(name = "id", value = "查询条件", required = true, dataType = "id", paramType = "body")
    @PostMapping(value = "/addDataAccess")
    public Result<Boolean> addDataAccess(@RequestParam("file") MultipartFile file,
                                         @RequestParam("fileName") String fileName,
                                         @RequestParam("fileType") String fileType,
                                         @RequestParam("fileFormat") String fileFormat,
                                         @RequestParam("dataType") Integer dataType) {
        return dataAccessService.addDataAccess(file,fileName,fileType,fileFormat,dataType);
    }

}
