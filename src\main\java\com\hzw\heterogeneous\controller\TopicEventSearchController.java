package com.hzw.heterogeneous.controller;

import com.hzw.heterogeneous.dto.TopicEventSearchDTO;
import com.hzw.heterogeneous.model.TopicEventSearch;
import com.hzw.heterogeneous.service.TopicEventSearchService;
import com.hzw.heterogeneous.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "主题事件列表")
@RestController
@RequestMapping("/topicEventSearch")
public class TopicEventSearchController {

    @Autowired
    private TopicEventSearchService topicEventSearchService;

    @ApiOperation(value = "主题事件列表")
    @PostMapping(value = "/listTopicEvent")
    public Result<List<TopicEventSearchDTO>> listTopicEvent(@RequestBody TopicEventSearch eventSearch) {
        return Result.ok(topicEventSearchService.listTopicEvent(eventSearch));
    }

    @ApiOperation(value = "新增主题事件")
    @PostMapping(value = "/addTopicEvent")
    public Result<Boolean> addTopicEvent(@RequestBody TopicEventSearch eventSearch) {
        if(StringUtils.isBlank(eventSearch.getEventName()) || StringUtils.isBlank(eventSearch.getKeywords())){
            return Result.failed("请输入主题分类以及关键词");
        }
        return Result.ok(topicEventSearchService.addTopicEvent(eventSearch));
    }

    @ApiOperation(value = "删除主题事件")
    @PostMapping(value = "/removeTopicEvent")
    public Result<Boolean> removeTopicEvent(@RequestBody TopicEventSearch eventSearch) {
        if(null == eventSearch.getId()){
            return Result.failed("请选择正确的数据进行操作");
        }
        return Result.ok(topicEventSearchService.removeTopicEvent(eventSearch));
    }

}
