package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_weapon")
public class Weapon {

    private String id;

    /**
     * 武器名称
     */
    private String name;
    
    /**
     * 武器英文名称
     */
    private String nameEng;

    /**
     * 代号
     */
    private String code;

    /**
     * 国家
     */
    private String country;

    /**
     * 参数
     */
    private String param;

    /**
     * 标签
     */
    private String tags;

    /**
     * 类型
     */
    private String type;
    
    /**
     * 更新时间
     */
    private Date updatedTime;
    
    /**
     * 关键词标签
     */
    private String keyTags;

    /**
     * 创建时间
     */
    private Date createdTime;
}
