package com.hzw.heterogeneous.controller.response;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @datetime 2024/05/14 14:15
 * @description: 文本检索vo
 * @version: 1.0
 */
@Data
public class TextSearchVo implements Serializable {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "正文")
    private String content;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "标签")
    private String tags;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "关键词标签")
    private String keyTags;

    @ApiModelProperty(value = "事件抽取")
    private Integer eventExtract;

    @ApiModelProperty(value = "时间")
    private String createdTime;


}
