SET FOREIGN_KEY_CHECKS=0;

ALTER TABLE `t_events` ADD COLUMN `key_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '关键词标签' AFTER `updated_time`;

CREATE TABLE `t_hot_topics`  (
  `id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
  `title` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL,
  `time` datetime NULL DEFAULT NULL,
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL,
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL,
  `tags` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '实体标签，多个实体采用;分隔',
  `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL,
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `key_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '关键词标签',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_bin ROW_FORMAT = Dynamic;

CREATE TABLE `t_hot_topics_news_relation`  (
  `id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
  `hot_topics_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL,
  `news_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL,
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `event_id`(`news_id` ASC) USING BTREE,
  INDEX `hot_topics_id`(`hot_topics_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_bin ROW_FORMAT = Dynamic;

ALTER TABLE `t_literature` ADD COLUMN `file_content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL COMMENT '文件正文内容，由大模型自动抽取' AFTER `updated_time`;

ALTER TABLE `t_literature` ADD COLUMN `key_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '关键词标签' AFTER `file_content`;

ALTER TABLE `t_news` ADD COLUMN `event_extract` int NULL DEFAULT NULL AFTER `updated_time`;

ALTER TABLE `t_news` ADD COLUMN `key_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '关键词标签' AFTER `event_extract`;

ALTER TABLE `t_person` ADD COLUMN `key_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '关键词标签' AFTER `updated_time`;

ALTER TABLE `t_person` ADD COLUMN `work_exp` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL COMMENT '工作经历' AFTER `key_tags`;

ALTER TABLE `t_weapon` ADD COLUMN `key_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '关键词标签' AFTER `updated_time`;

ALTER TABLE `t_weapon` ADD COLUMN `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '国家和地区' AFTER `key_tags`;

ALTER TABLE `t_hot_topics_news_relation` ADD CONSTRAINT `t_hot_topics_news_relation_ibfk_1` FOREIGN KEY (`news_id`) REFERENCES `t_news` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE `t_hot_topics_news_relation` ADD CONSTRAINT `t_hot_topics_news_relation_ibfk_2` FOREIGN KEY (`hot_topics_id`) REFERENCES `t_hot_topics` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE `t_events` MODIFY COLUMN `tags` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '实体标签，多个实体采用;分隔' AFTER `content`;

ALTER TABLE `t_literature` MODIFY COLUMN `tags` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '实体标签，多个实体采用;分隔' AFTER `source`;

ALTER TABLE `t_literature` MODIFY COLUMN `file_path` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '文件原文路径' AFTER `tags`;

ALTER TABLE `t_news` MODIFY COLUMN `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL AFTER `title`;

ALTER TABLE `t_news` MODIFY COLUMN `url` varchar(10240) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL AFTER `tags`;

ALTER TABLE `t_person` MODIFY COLUMN `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '级别字段' AFTER `job_title`;

ALTER TABLE `t_person` MODIFY COLUMN `tags` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '实体标签，多个实体采用;分隔' AFTER `type`;

ALTER TABLE `t_person` MODIFY COLUMN `graduate_school` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL COMMENT '教育经历' AFTER `tags`;

ALTER TABLE `t_person_weapon_picture_rel` MODIFY COLUMN `picture_type` tinyint NULL DEFAULT NULL COMMENT '2代表武器 3代表人物' AFTER `picture_id`;

ALTER TABLE `t_person_weapon_video_rel` MODIFY COLUMN `video_type` tinyint NULL DEFAULT NULL COMMENT '2代表武器 3代表人物' AFTER `video_id`;

ALTER TABLE `t_picture` MODIFY COLUMN `tags` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '实体标签，多个实体采用;分隔' AFTER `type`;

ALTER TABLE `t_video` MODIFY COLUMN `tags` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '实体标签，多个实体采用;分隔' AFTER `type`;

ALTER TABLE `t_weapon` MODIFY COLUMN `tags` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL COMMENT '实体标签，多个实体采用;分隔' AFTER `type`;

SET FOREIGN_KEY_CHECKS=1;

