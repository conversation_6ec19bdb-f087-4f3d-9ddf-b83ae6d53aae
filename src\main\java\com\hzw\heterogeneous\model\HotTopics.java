package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* HotTopics实体类 - 热点话题表
* 对应数据库表: t_hot_topics
* Created by pj on 2024/11/12
*/
@ApiModel("t_hot_topics表 - 热点话题")
@TableName("t_hot_topics")
@Data
public class HotTopics implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 标题
     */
    private String title;

    /**
     * 时间
     */
    private Date time;

    /**
     * 地点
     */
    private String location;

    /**
     * 类型
     */
    private String type;

    /**
     * 内容
     */
    private String content;

    /**
     * 标签
     */
    private String tags;

    /**
     * 来源
     */
    private String source;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 关键词标签
     */
    private String keyTags;

    private static final long serialVersionUID = 1L;
}