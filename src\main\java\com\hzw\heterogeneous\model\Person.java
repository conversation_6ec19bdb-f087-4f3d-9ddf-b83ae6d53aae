package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_person")
public class Person {

    /**
     * id
     */
    private String id;
    /**
     * 用户姓名
     */
    private String userName;
    
    /**
     * 用户英文姓名
     */
    private String userNameEng;

    /**
     * 国家
     */
    private String country;
    
    /**
     * 职位标题
     */
    private String jobTitle;

    /**
     * 职务
     */
    private String position;

    /**
     * 任职任期
     */
    private String term;

    /**
     * 毕业院校
     */
    private String graduateSchool;

    /**
     * 类型
     */
    private String type;

    /**
     * 履历
     */
    private String resume;

    /**
     * 标签
     */
    private String tags;
    
    /**
     * 创建时间
     */
    private Date createdTime;
    
    /**
     * 更新时间
     */
    private Date updatedTime;
    
    /**
     * 关键词标签
     */
    private String keyTags;
    
    /**
     * 工作经历
     */
    private String workExp;
}
