package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("t_topic_event_search")
@ApiModel(value = "TopicEventSearch对象", description = "主题事件分类检索")
public class TopicEventSearch implements Serializable {

    private String id;

    @ApiModelProperty(value = "事件分类")
    private String eventName;

    @ApiModelProperty(value = "关键词")
    private String keywords;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    @ApiModelProperty(value = "修改时间")
    private Date updatedTime;

    @ApiModelProperty(value = "是否删除 0 否 1 是")
    private Integer isDelete;

    @ApiModelProperty(value = "备注")
    private String remark;

}
