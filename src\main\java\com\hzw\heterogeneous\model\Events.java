package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* Created by pj on 2024/11/12
*/
@ApiModel("t_events表")
@TableName("t_events")
@Data
public class Events implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 标题
     */
    private String title;

    /**
     * 时间
     */
    private Date time;

    /**
     * 地点
     */
    private String location;

    /**
     * 类型
     */
    private String type;

    /**
     * 内容
     */
    private String content;

    /**
     * 标签
     */
    private String tags;

    /**
     * 来源
     */
    private String source;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 关键词标签
     */
    private String keyTags;

    private static final long serialVersionUID = 1L;
}