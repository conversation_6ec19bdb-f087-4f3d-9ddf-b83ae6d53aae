package com.hzw.heterogeneous.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 推荐列表响应对象
 */
@Data
@ApiModel("推荐列表结果")
public class RecommendationVo {
    
    @ApiModelProperty("用户列表")
    private List<PersonItem> users;
    
    @ApiModelProperty("武器列表")
    private List<WeaponItem> wepons;
    
    /**
     * 人物项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel("人物项")
    public static class PersonItem {
        
        @ApiModelProperty("人物ID")
        private String id;
        
        @ApiModelProperty("人物姓名")
        private String userName;
        
        @ApiModelProperty("人物英文姓名")
        private String userNameEng;
    }
    
    /**
     * 武器项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel("武器项")
    public static class WeaponItem {
        
        @ApiModelProperty("武器ID")
        private String id;
        
        @ApiModelProperty("武器名称")
        private String name;
        
        @ApiModelProperty("武器英文名称")
        private String nameEng;
    }
}