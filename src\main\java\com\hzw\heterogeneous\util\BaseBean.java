package com.hzw.heterogeneous.util;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021年04月07日 13:46
 * @Version 1.0
 */
@Data
public class BaseBean extends OverrideBeanMethods {

    @ApiModelProperty(value = "创建人，保存用户ID值", position = 1000)
    @TableField(fill = FieldFill.INSERT)
    private Long createdUserId;

    @ApiModelProperty(value = "创建日期", position = 1001)
    @JsonFormat
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;

    @ApiModelProperty(value = "最后修改人，保存用户ID值", position = 1002)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUserId;

    @ApiModelProperty(value = "最后修改日期", position = 1003)
    @JsonFormat
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedTime;

    @ApiModelProperty(value = "删除标记，字典数据，例如：0：否、1：是", example = "1", position = 1004)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "版本", position = 1005)
    @Version
    private Integer version;

    @ApiModelProperty(value = "备注", position = 1006)
    private String remark;
}
