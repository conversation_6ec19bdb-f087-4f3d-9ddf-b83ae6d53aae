package com.hzw.heterogeneous.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hzw.heterogeneous.constant.FormatConstants;
import com.hzw.heterogeneous.constant.PageConstants;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 分页查询参数封装，用于继承
 *
 * <AUTHOR>
 * @version 1.0.0 2021-03-31
 */

public abstract class PagingCondition extends OverrideBeanMethods {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -5826914803574438815L;

    @ApiModelProperty(value = "每页显示多少条记录", example = "20", position = 1000)
    private Long pageSize = PageConstants.PAGE_SIZE;

    @ApiModelProperty(value = "当前页", example = "1", position = 1001)
    private Long page = 1L;

    @ApiModelProperty(value = "排序字段数组,支持多个和下面排序并存", position = 1002)
    private List<String> sortField;

    @ApiModelProperty(value = "排序方式，true升序false降序和上面对应", position = 1003)
    private List<Boolean> sortOrder;

    @ApiModelProperty(value = "排序sql", position = 1004)
    private String sortOrderSql;


    @ApiModelProperty(value = "创建人，保存用户ID值", position = 1000)
    private Long createdUserId;

    @ApiModelProperty(value = "创建日期", position = 1001)
    @JsonFormat
    private Date createdTime;

    @ApiModelProperty(value = "最后修改人，保存用户ID值", position = 1002)
    private Long updatedUserId;

    @ApiModelProperty(value = "最后修改日期", position = 1003)
    @JsonFormat
    private Date updatedTime;

    @ApiModelProperty(value = "删除标记，字典数据，例如：0：否、1：是", example = "1", position = 1004)
    private Integer isDelete;

    @ApiModelProperty(value = "版本", position = 1005)
    private Integer version;

    @ApiModelProperty(value = "备注", position = 1006)
    private String remark;

    public Long getCreatedUserId() {
        return createdUserId;
    }

    public void setCreatedUserId(Long createdUserId) {
        this.createdUserId = createdUserId;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Long getUpdatedUserId() {
        return updatedUserId;
    }

    public void setUpdatedUserId(Long updatedUserId) {
        this.updatedUserId = updatedUserId;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getSortField() {
        return sortField;
    }

    public void setSortField(List<String> sortField) {
        this.sortField = sortField;
    }

    public List<Boolean> getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(List<Boolean> sortOrder) {
        this.sortOrder = sortOrder;
    }

    /**
     * 获取每页显示多少条记录
     *
     * @return 每页显示多少条记录
     */
    public Long getPageSize() {
        return this.pageSize;
    }

    /**
     * 设置每页显示多少条记录
     *
     * @param pageSize 每页显示多少条记录
     */
    public void setPageSize(Long pageSize) {
        if (pageSize != null && pageSize > 0) {
            this.pageSize = pageSize;
        }
    }

    /**
     * 获取当前页
     *
     * @return 当前页
     */
    public Long getPage() {
        return this.page;
    }

    /**
     * 设置当前页
     *
     * @param page 当前页
     */
    public void setPage(Long page) {
        if (page != null && page > 0) {
            this.page = page;
        }
    }


    /**
     * 创建简单分页模型
     */
    public <T> Page<T> buildPage() {
        return new Page<>(page, pageSize);
    }

    /**
     * 自定义简单分页模型
     */
    public <T> Page<T> customizeBuildPage() {
        setSortOrderSql();
        return new Page<>(page, pageSize);
    }

    public String getSortOrderSql() {
        return sortOrderSql;
    }

    /**
     * @param
     * <AUTHOR>
     * @Description sql排序预处理
     * @Return void
     * @Date 2021-04-28 9:50
     */
    public void setSortOrderSql() {
        StringBuffer sb = new StringBuffer();
        if (!CollectionUtils.isEmpty(this.getSortField()) && !CollectionUtils.isEmpty(this.getSortOrder()) && sortField.size() == sortOrder.size()) {
            for (int i = 0; i < sortField.size(); i++) {
                String field = sortField.get(i);
                Boolean order = sortOrder.get(i);
                if (order) {
                    sb.append(field).append(" asc,");
                } else {
                    sb.append(field).append(" desc,");
                }
            }
        } else {
            sb.append(FormatConstants.CREATED_TIME).append(" desc,");
        }
        String orderType = sb.toString().substring(0, sb.toString().length() - 1);
        this.sortOrderSql = orderType;
    }
}
