package com.hzw.heterogeneous.service;


import com.hzw.heterogeneous.controller.response.TopicEventSearchVo;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import com.hzw.heterogeneous.dto.TopicEventSearchDTO;
import com.hzw.heterogeneous.model.TopicEventSearch;
import com.hzw.heterogeneous.util.Paging;

import java.util.List;

public interface TopicEventSearchService {
    /**
     * 分页查询主题事件
     * @param condition
     * @return
     */
    Paging<TopicEventSearchVo> listPage(ThematicAnalysisCondition condition);

    /**
     * 查询主题事件列表
     * @param eventSearch
     * @return
     */
    List<TopicEventSearchDTO> listTopicEvent(TopicEventSearch eventSearch);

    /**
     * 新增主题事件列表
     * @param eventSearch
     * @return
     */
    Boolean addTopicEvent(TopicEventSearch eventSearch);

    /**
     * 删除主题事件列表
     * @param eventSearch
     * @return
     */
    Boolean removeTopicEvent(TopicEventSearch eventSearch);

    /**
     * 获取导出数据
     * @param ids
     * @return
     */
    List<TopicEventSearchVo> exportList(List<String> ids);

    TopicEventSearchVo getTopicEventInfo(String id);
}
