package com.hzw.heterogeneous.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* Created by pj on 2024/11/12
*/
@ApiModel("t_tags表")
@TableName("t_tags")
@Data
public class Tags implements Serializable {
    private String id;

    /**
     * 标签内容
     *
     * @mbg.generated
     */
    private String tagContent;

    /**
     * 标签实体类型(1 人物  2 武器  3 图片  4 新闻  5 时间  6 热点资讯  7 文献')
     *
     * @mbg.generated
     */
    private Integer tagEntity;

    /**
     * 创建时间
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     * 更新时间
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * 逻辑删除0正常1删除
     *
     * @mbg.generated
     */
    private Integer isDelete;

    /**
     * 备注
     *
     * @mbg.generated
     */
    private String remark;

    private static final long serialVersionUID = 1L;
}