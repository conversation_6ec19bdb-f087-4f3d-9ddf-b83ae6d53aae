<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.heterogeneous.mapper.PersonMapper">

    <resultMap id="personResultMap" type="com.hzw.heterogeneous.controller.response.PersonVo">
        <id property="id" column="id"/>
        <result property="userName" column="user_name"/>
        <result property="country" column="country"/>
        <result property="position" column="position"/>
        <result property="term" column="term"/>
        <result property="graduateSchool" column="graduate_school"/>
        <result property="type" column="type"/>
        <result property="resume" column="resume"/>
        <result property="keyTags" column="key_tags"/>
        <result property="workExp" column="work_exp"/>
    </resultMap>

    <select id="listPersonNum" resultType="com.hzw.heterogeneous.controller.response.EntityTypeNumVo">
        SELECT '政治' AS entityType, COUNT(*) AS num FROM t_person WHERE type = '政治'
        UNION ALL
        SELECT '经济' AS entityType, COUNT(*) AS num FROM t_person WHERE type = '经济'
        UNION ALL
        SELECT '军事' AS entityType, COUNT(*) AS num FROM t_person WHERE type = '军事'
        UNION ALL
        SELECT '政要' AS entityType, COUNT(*) AS num FROM t_person WHERE type = '政要'
        UNION ALL
        SELECT '企业CEO' AS entityType, COUNT(*) AS num FROM t_person WHERE type = '企业CEO'
    </select>
    <select id="listPersonPage" resultType="com.hzw.heterogeneous.controller.response.PersonVo">
        SELECT
            id,
            user_name AS userName,
            country,
            position,
            term,
            graduate_school AS graduateSchool,
            type,
            resume,
            key_tags AS keyTags,
            work_exp AS workExp
        FROM
            t_person
        WHERE 
            1 = 1
        <if test="condition.keyWords != null and condition.keyWords != ''">
            AND (
                user_name LIKE CONCAT('%', #{condition.keyWords}, '%')
                OR user_name_eng LIKE CONCAT('%', #{condition.keyWords}, '%')
            )
        </if>
        <if test="condition.type != null and condition.type != ''">
            AND type = #{condition.type}
        </if>
        ORDER BY id DESC
    </select>

    <select id="getPersonInfo" resultMap="personResultMap">
        SELECT
        id,
        user_name,
        country,
        position,
        term,
        graduate_school,
        type,
        resume,
        key_tags,
        work_exp
        FROM
        t_person
        WHERE id = #{id}
    </select>

    <select id="selectByPaths" resultType="com.hzw.heterogeneous.model.Person">
        SELECT
        p.id,
        p.user_name AS userName,
        pic.tags,
        p.created_time AS createdTime
        FROM
        t_person p
        INNER JOIN t_person_weapon_picture_rel rel ON p.id = rel.person_weapon_id AND rel.picture_type = 1
        INNER JOIN t_picture pic ON pic.id = rel.picture_id
        WHERE
        pic.url IN
        <foreach item="path" collection="paths" open="(" separator="," close=")">
            #{path}
        </foreach>
        AND p.id IS NOT NULL
    </select>

    <select id="searchPersonsByKeyword" resultType="com.hzw.heterogeneous.model.Person">
        SELECT
            id,
            user_name as userName,
            user_name_eng as userNameEng
        FROM
            t_person
        WHERE
            1 = 1
            AND (
                user_name LIKE CONCAT('%', #{keyword}, '%')
                OR user_name_eng LIKE CONCAT('%', #{keyword}, '%')
            )
        ORDER BY id DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

</mapper>