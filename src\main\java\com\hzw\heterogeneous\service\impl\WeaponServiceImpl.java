package com.hzw.heterogeneous.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.response.EntityTypeNumVo;
import com.hzw.heterogeneous.controller.response.WeaponVo;
import com.hzw.heterogeneous.mapper.*;
import com.hzw.heterogeneous.model.*;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import com.hzw.heterogeneous.service.WeaponService;
import com.hzw.heterogeneous.util.Paging;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WeaponServiceImpl implements WeaponService {

    private static final Logger logger = LoggerFactory.getLogger(WeaponServiceImpl.class);

    @Autowired
    private WeaponMapper weaponMapper;

    @Autowired
    private PersonWeaponPictureRelMapper pictureRelMapper;
    
    @Autowired
    private PersonWeaponVideoRelMapper videoRelMapper;
    
    @Autowired
    private PictureMapper pictureMapper;
    
    @Autowired
    private VideoMapper videoMapper;
    
    @Autowired
    private WeaponTrendMapper weaponTrendMapper;
    
    @Autowired
    private TrendEventRelationMapper trendEventRelationMapper;
    
    @Autowired
    private EventsMapper eventsMapper;
    /**
     * 目标实体类型以及数量
     * @param condition
     * @return
     */
    @Override
    public List<EntityTypeNumVo> listWeaponNum(ThematicAnalysisCondition condition) {
        return weaponMapper.listWeaponNum();
    }

    /**
     * 分页查询目标实体
     * @param condition
     * @return
     */
    @Override
    public Paging<WeaponVo> listWeaponPage(ThematicAnalysisCondition condition) {
        logger.info("开始查询武器列表，查询条件: {}", condition);
        
        IPage<WeaponVo> page = condition.buildPage();
        page = weaponMapper.listWeaponPage(page, condition);
        
        // 加载武器列表的图片和视频信息
        for (WeaponVo weaponVo : page.getRecords()) {
            loadWeaponMediaInfo(weaponVo);
        }
        
        logger.info("武器列表查询完成，共查询到 {} 条数据", page.getRecords().size());
        return Paging.buildPaging(page);
    }

    /**
     * 获取目标实体详情
     * @param condition
     * @return
     */
    @Override
    public WeaponVo getWeaponInfo(ThematicAnalysisCondition condition) {
        logger.info("开始查询武器详情，ID: {}", condition.getId());
        
        if (StringUtils.isEmpty(condition.getId())) {
            logger.error("武器ID不能为空");
            throw new IllegalArgumentException("武器ID不能为空");
        }
        
        try {
            // 1. 查询武器基本信息
            WeaponVo weaponInfo = weaponMapper.getWeaponInfo(condition.getId());
            if (weaponInfo == null) {
                logger.warn("未找到ID为 {} 的武器信息", condition.getId());
                return null;
            }
            
            // 2. 加载图片和视频信息
            loadWeaponMediaInfo(weaponInfo);
            
            // 3. 加载武器动向及关联事件
            loadWeaponTrendsWithEvents(weaponInfo);
            
            logger.info("武器详情查询完成，武器名称: {}", weaponInfo.getWeaponName());
            return weaponInfo;
            
        } catch (Exception e) {
            logger.error("查询武器详情失败，ID: {}, 错误信息: {}", condition.getId(), e.getMessage(), e);
            throw new RuntimeException("查询武器详情失败", e);
        }
    }

    @Override
    public List<WeaponVo> exportList(List<String> ids) {
        logger.info("开始导出武器数据，导出ID数量: {}", ids != null ? ids.size() : 0);
        
        if (CollectionUtils.isEmpty(ids)) {
            logger.warn("导出武器数据失败：导出ID列表为空");
            return new ArrayList<>();
        }
        
        try {
            // 1. 查询武器基本信息
            LambdaQueryWrapper<Weapon> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Weapon::getId, ids);
            List<Weapon> weapons = weaponMapper.selectList(queryWrapper);
            
            if (CollectionUtils.isEmpty(weapons)) {
                logger.warn("未找到对应的武器数据，IDs: {}", ids);
                return new ArrayList<>();
            }
            
            List<WeaponVo> weaponVos = new ArrayList<>();
            
            // 2. 转换为 WeaponVo 并设置正确的字段映射
            for (Weapon weapon : weapons) {
                WeaponVo weaponVo = new WeaponVo();
                BeanUtils.copyProperties(weapon, weaponVo);
                
                // 手动设置武器名称映射
                weaponVo.setWeaponName(weapon.getName());
                weaponVo.setWeaponNameEng(weapon.getNameEng());
                
                // 对于导出功能，可以选择不加载多媒体和动向数据以提高性能
                // 如果需要完整数据，可以取消以下注释：
                // loadWeaponMediaInfo(weaponVo);
                // loadWeaponTrendsWithEvents(weaponVo);
                
                weaponVos.add(weaponVo);
            }
            
            logger.info("成功导出武器数据，实际导出数量: {}", weaponVos.size());
            return weaponVos;
            
        } catch (Exception e) {
            logger.error("导出武器数据失败，IDs: {}", ids, e);
            throw new RuntimeException("导出武器数据失败", e);
        }
    }

    /**
     * 加载武器图片和视频信息
     * @param weaponVo 武器信息
     */
    private void loadWeaponMediaInfo(WeaponVo weaponVo) {
        if (weaponVo == null || StringUtils.isEmpty(weaponVo.getId())) {
            return;
        }
        
        try {
            // 加载图片信息
            List<PersonWeaponPictureRel> pictureRels = pictureRelMapper.selectList(
                new LambdaQueryWrapper<PersonWeaponPictureRel>()
                    .eq(PersonWeaponPictureRel::getPersonWeaponId, weaponVo.getId())
                    .eq(PersonWeaponPictureRel::getPictureType, 2) // 2代表武器
            );
            
            if (!CollectionUtils.isEmpty(pictureRels)) {
                List<String> pictureIds = pictureRels.stream()
                    .map(PersonWeaponPictureRel::getPictureId)
                    .collect(Collectors.toList());
                
                List<Picture> pictures = pictureMapper.selectBatchIds(pictureIds);
                weaponVo.setPictures(pictures);
                
                // 设置首张图片作为预览图
                if (!CollectionUtils.isEmpty(pictures)) {
                    weaponVo.setUrl(pictures.get(0).getUrl());
                }
                
                logger.debug("武器 {} 加载了 {} 张图片", weaponVo.getId(), pictures.size());
            }
            
            // 加载视频信息
            List<PersonWeaponVideoRel> videoRels = videoRelMapper.selectList(
                new LambdaQueryWrapper<PersonWeaponVideoRel>()
                    .eq(PersonWeaponVideoRel::getPersonWeaponId, weaponVo.getId())
                    .eq(PersonWeaponVideoRel::getVideoType, 2) // 2代表武器
            );
            
            if (!CollectionUtils.isEmpty(videoRels)) {
                List<String> videoIds = videoRels.stream()
                    .map(PersonWeaponVideoRel::getVideoId)
                    .collect(Collectors.toList());
                
                List<Video> videos = videoMapper.selectBatchIds(videoIds);
                weaponVo.setVideos(videos);
                
                logger.debug("武器 {} 加载了 {} 个视频", weaponVo.getId(), videos.size());
            }
            
        } catch (Exception e) {
            logger.error("加载武器 {} 的多媒体信息失败", weaponVo.getId(), e);
        }
    }

    /**
     * 加载武器动向及关联事件
     * @param weaponVo 武器信息
     */
    private void loadWeaponTrendsWithEvents(WeaponVo weaponVo) {
        if (weaponVo == null || StringUtils.isEmpty(weaponVo.getId())) {
            return;
        }
        
        try {
            // 查询武器动向（按创建时间倒序）
            List<WeaponTrend> weaponTrends = weaponTrendMapper.selectList(
                new LambdaQueryWrapper<WeaponTrend>()
                    .eq(WeaponTrend::getWeaponId, weaponVo.getId())
                    .orderByDesc(WeaponTrend::getCreatedTime)
            );
            
            if (!CollectionUtils.isEmpty(weaponTrends)) {
                // 为每个动向加载关联的事件
                for (WeaponTrend trend : weaponTrends) {
                    List<Events> relatedEvents = getTrendRelatedEvents(trend.getId());
                    trend.setRelatedEvents(relatedEvents);
                }
                
                weaponVo.setWeaponTrends(weaponTrends);
                logger.debug("武器 {} 加载了 {} 个动向", weaponVo.getId(), weaponTrends.size());
            }
            
        } catch (Exception e) {
            logger.error("加载武器 {} 的动向信息失败", weaponVo.getId(), e);
        }
    }

    /**
     * 查询动向关联的事件
     * @param trendId 动向ID
     * @return 关联事件列表（按时间正序排列）
     */
    private List<Events> getTrendRelatedEvents(String trendId) {
        if (StringUtils.isEmpty(trendId)) {
            return new ArrayList<>();
        }
        
        try {
            // 1. 查询动向与事件的关联关系
            List<TrendEventRelation> relations = trendEventRelationMapper.selectList(
                new LambdaQueryWrapper<TrendEventRelation>()
                    .eq(TrendEventRelation::getTrendId, trendId)
            );
            
            if (CollectionUtils.isEmpty(relations)) {
                return new ArrayList<>();
            }
            
            // 2. 获取事件ID列表
            List<String> eventIds = relations.stream()
                .map(TrendEventRelation::getEventId)
                .collect(Collectors.toList());
            
            // 3. 查询事件详情（按时间正序排列）
            List<Events> events = eventsMapper.selectList(
                new LambdaQueryWrapper<Events>()
                    .in(Events::getId, eventIds)
                    .orderByAsc(Events::getTime)
            );
            
            logger.debug("动向 {} 关联了 {} 个事件", trendId, events.size());
            return events;
            
        } catch (Exception e) {
            logger.error("查询动向 {} 关联事件失败", trendId, e);
            return new ArrayList<>();
        }
    }
}
