package com.hzw.heterogeneous.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.request.DataAccessCondition;
import com.hzw.heterogeneous.controller.response.DataAccessVo;
import com.hzw.heterogeneous.controller.response.TextSearchVo;
import com.hzw.heterogeneous.model.DataAccess;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 数据接入 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Mapper
public interface DataAccessMapper extends BaseMapper<DataAccess> {

    /**
     * 分页查询数据接入
     * @param condition
     * @return
     */
    IPage<DataAccessVo> listPage(@Param("page") IPage<TextSearchVo> page, @Param("condition") DataAccessCondition condition);
}
