package com.hzw.heterogeneous.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.function.Function;

/**
 * 扩展的分页接口实现，支持添加自定义属性
 *
 * @param <T> 数据类型
 * <AUTHOR>
 * @version 1.0
 */
public class ExtendedPage<T> implements IPage<T> {

    private final IPage<T> originalPage;

    @ApiModelProperty(value = "搜索关键词参数")
    @JsonProperty("keyWordsParam")
    private String keyWordsParam;

    public ExtendedPage(IPage<T> originalPage) {
        this.originalPage = originalPage;
    }

    public ExtendedPage(IPage<T> originalPage, String keyWordsParam) {
        this.originalPage = originalPage;
        this.keyWordsParam = keyWordsParam;
    }

    /**
     * 获取搜索关键词参数
     */
    public String getKeyWordsParam() {
        return keyWordsParam;
    }

    /**
     * 设置搜索关键词参数
     */
    public ExtendedPage<T> setKeyWordsParam(String keyWordsParam) {
        this.keyWordsParam = keyWordsParam;
        return this;
    }

    // 以下方法委托给原始页面对象
    @Override
    public List<T> getRecords() {
        return originalPage.getRecords();
    }

    @Override
    public IPage<T> setRecords(List<T> records) {
        originalPage.setRecords(records);
        return this;
    }

    @Override
    public long getTotal() {
        return originalPage.getTotal();
    }

    @Override
    public IPage<T> setTotal(long total) {
        originalPage.setTotal(total);
        return this;
    }

    @Override
    public long getSize() {
        return originalPage.getSize();
    }

    @Override
    public IPage<T> setSize(long size) {
        originalPage.setSize(size);
        return this;
    }

    @Override
    public long getCurrent() {
        return originalPage.getCurrent();
    }

    @Override
    public IPage<T> setCurrent(long current) {
        originalPage.setCurrent(current);
        return this;
    }

    @Override
    public List<OrderItem> orders() {
        return originalPage.orders();
    }

    @Override
    public boolean searchCount() {
        return originalPage.searchCount();
    }

    // 注释掉不存在的方法
    /*
    @Override
    public IPage<T> setSearchCount(boolean searchCount) {
        originalPage.setSearchCount(searchCount);
        return this;
    }
    */

    @Override
    public boolean optimizeCountSql() {
        return originalPage.optimizeCountSql();
    }

    // 注释掉不存在的方法
    /*
    @Override
    public IPage<T> setOptimizeCountSql(boolean optimizeCountSql) {
        originalPage.setOptimizeCountSql(optimizeCountSql);
        return this;
    }
    */

    @Override
    public long getPages() {
        return originalPage.getPages();
    }

    @Override
    public long offset() {
        return originalPage.offset();
    }

    @Override
    public String countId() {
        return originalPage.countId();
    }

    @Override
    public <R> IPage<R> convert(Function<? super T, ? extends R> mapper) {
        IPage<R> convertedPage = originalPage.convert(mapper);
        return new ExtendedPage<>(convertedPage, this.keyWordsParam);
    }

    /**
     * 静态工厂方法，用于创建带有keyWordsParam的扩展页面
     *
     * @param originalPage 原始分页对象
     * @param keyWordsParam 关键词参数
     * @return 扩展的分页对象
     */
    public static <T> ExtendedPage<T> of(IPage<T> originalPage, String keyWordsParam) {
        return new ExtendedPage<>(originalPage, keyWordsParam);
    }
}
