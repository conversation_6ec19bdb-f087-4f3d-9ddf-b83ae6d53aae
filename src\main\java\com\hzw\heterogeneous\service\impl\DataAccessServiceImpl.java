package com.hzw.heterogeneous.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.heterogeneous.controller.request.DataAccessCondition;
import com.hzw.heterogeneous.controller.request.DatabaseConnectionReq;
import com.hzw.heterogeneous.controller.request.DataSyncReq;
import com.hzw.heterogeneous.controller.response.DataAccessVo;
import com.hzw.heterogeneous.controller.response.DataSyncVo;
import com.hzw.heterogeneous.controller.response.DatabaseConnectionTestVo;
import com.hzw.heterogeneous.controller.response.SyncedDataVo;
import com.hzw.heterogeneous.mapper.DataAccessMapper;
import com.hzw.heterogeneous.model.DataAccess;
import com.hzw.heterogeneous.service.DataAccessService;
import com.hzw.heterogeneous.util.Paging;
import com.hzw.heterogeneous.util.Result;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 * 数据接入 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Slf4j
@Service
public class DataAccessServiceImpl extends ServiceImpl<DataAccessMapper, DataAccess> implements DataAccessService {

    @Value("${files.temporary.path}")
    private String tempFilePath;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DataSource dataSource;

    // 存储同步任务状态的内存缓存
    private final Map<String, DataSyncVo> syncTaskCache = new ConcurrentHashMap<>();

    @Override
    public IPage<DataAccessVo> listPage(DataAccessCondition condition) {
        return this.baseMapper.listPage(condition.buildPage(), condition);
    }

    @Override
    public Result<Boolean> addDataAccess(MultipartFile file, String fileName,String fileType,String fileFormat,Integer dataType) {
        if (file.isEmpty()) {
            return Result.failed("文件不存在");
        }
        String originalFilename = file.getOriginalFilename();
        try {
//            Path filePath = Paths.get(tempFilePath, file.getOriginalFilename());
//            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            // 清理文件名，防止路径穿越攻击
            String safeFileName = sanitizeFileName(originalFilename);
            Path targetDir = Paths.get(tempFilePath);

            // 确保目录存在
            if (!Files.exists(targetDir)) {
                Files.createDirectories(targetDir);
            }

            Path filePath = targetDir.resolve(safeFileName);
            try (InputStream inputStream = file.getInputStream()) {
                Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
            }
            DataAccess dataAccess = new DataAccess();
            dataAccess.setDataType(dataType);
            dataAccess.setFileName(fileName);
            dataAccess.setFileType(fileType);
            dataAccess.setCreatedTime(new Date());
            dataAccess.setFileFormat(fileFormat);
            dataAccess.setFileUrl(filePath.toString());
            this.save(dataAccess);
        } catch (IOException e) {
            log.error("文件接入新增失败",e);
            return Result.failed("文件接入新增失败");
        }
        return Result.ok();
    }

    // 文件名清理函数，防止路径穿越
    private String sanitizeFileName(String filename) {
        if (filename == null || filename.isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        // 替换掉非法字符，例如 ../ 或 / 等
        return filename.replaceAll("[\\\\/]", "_");
    }

    // ==================== 数据同步相关方法实现 ====================

    @Override
    public Result<DatabaseConnectionTestVo> testDatabaseConnection(DatabaseConnectionReq connectionReq) {
        log.info("开始测试数据库连接，类型：{}，地址：{}:{}",
                connectionReq.getDbType(), connectionReq.getHost(), connectionReq.getPort());

        DatabaseConnectionTestVo testResult = new DatabaseConnectionTestVo();
        long startTime = System.currentTimeMillis();

        try {
            if (connectionReq.getDbType() == 3) { // MongoDB
                return testMongoConnection(connectionReq, testResult, startTime);
            } else { // MySQL 或 Oracle
                return testJdbcConnection(connectionReq, testResult, startTime);
            }
        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            testResult.setSuccess(false);
            testResult.setErrorMessage("连接测试失败: " + e.getMessage());
            testResult.setResponseTime(System.currentTimeMillis() - startTime);
            return Result.ok(testResult);
        }
    }

    @Override
    public Result<DataSyncVo> executeDataSync(DataSyncReq syncReq) {
        log.info("开始执行数据同步，源数据库类型：{}，目标数据库：{}，目标表：{}",
                syncReq.getSourceDatabase().getDbType(), syncReq.getTargetDatabase(), syncReq.getTargetTable());

        // 生成任务ID
        String taskId = UUID.randomUUID().toString();

        // 创建同步任务对象
        DataSyncVo syncTask = createSyncTask(taskId, syncReq);
        syncTaskCache.put(taskId, syncTask);

        // 异步执行同步任务
        CompletableFuture.runAsync(() -> performDataSync(taskId, syncReq));

        return Result.ok(syncTask);
    }

    @Override
    public Result<DataSyncVo> getSyncTaskStatus(String taskId) {
        DataSyncVo syncTask = syncTaskCache.get(taskId);
        if (syncTask == null) {
            return Result.failed("同步任务不存在");
        }
        return Result.ok(syncTask);
    }

    @Override
    public Result<Paging<SyncedDataVo>> listSyncedData(String targetDatabase, String targetTable, Integer current, Integer size) {
        log.info("查询同步数据，目标数据库：{}，目标表：{}，页码：{}，大小：{}",
                targetDatabase, targetTable, current, size);

        try {
            // 构建查询SQL
            String tableName = "`" + targetDatabase + "`.`" + targetTable + "`";
            String countSql = "SELECT COUNT(*) FROM " + tableName;
            String dataSql = "SELECT * FROM " + tableName + " LIMIT ?, ?";

            // 查询总数
            Long total = jdbcTemplate.queryForObject(countSql, Long.class);
            if (total == null) total = 0L;

            // 查询数据
            int offset = (current - 1) * size;
            List<Map<String, Object>> dataList = jdbcTemplate.queryForList(dataSql, offset, size);

            // 转换为SyncedDataVo
            List<SyncedDataVo> syncedDataList = new ArrayList<>();
            for (Map<String, Object> row : dataList) {
                SyncedDataVo syncedData = new SyncedDataVo();
                syncedData.setId(String.valueOf(row.get("id")));
                syncedData.setTargetData(JSON.toJSONString(row));
                syncedData.setTargetTable(targetTable);
                syncedData.setSyncTime(new java.util.Date());
                syncedDataList.add(syncedData);
            }

            // 构建分页结果
            Page<SyncedDataVo> page = new Page<>(current, size);
            page.setTotal(total);
            page.setRecords(syncedDataList);

            return Result.ok(Paging.buildPaging(page));

        } catch (Exception e) {
            log.error("查询同步数据失败", e);
            return Result.failed("查询同步数据失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Paging<DataSyncVo>> listSyncTasks(Integer current, Integer size) {
        log.info("查询同步任务列表，页码：{}，大小：{}", current, size);

        List<DataSyncVo> allTasks = new ArrayList<>(syncTaskCache.values());

        // 按创建时间倒序排序
        allTasks.sort((a, b) -> b.getCreatedTime().compareTo(a.getCreatedTime()));

        // 分页处理
        int total = allTasks.size();
        int start = (current - 1) * size;
        int end = Math.min(start + size, total);

        List<DataSyncVo> pageData = start < total ? allTasks.subList(start, end) : new ArrayList<>();

        Page<DataSyncVo> page = new Page<>(current, size);
        page.setTotal(total);
        page.setRecords(pageData);

        return Result.ok(Paging.buildPaging(page));
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 测试JDBC连接（MySQL/Oracle）
     */
    private Result<DatabaseConnectionTestVo> testJdbcConnection(DatabaseConnectionReq connectionReq,
                                                               DatabaseConnectionTestVo testResult,
                                                               long startTime) {
        Connection connection = null;
        try {
            // 加载驱动
            Class.forName(connectionReq.getDriverClassName());

            // 建立连接
            connection = DriverManager.getConnection(
                connectionReq.getJdbcUrl(),
                connectionReq.getUsername(),
                connectionReq.getPassword()
            );

            testResult.setSuccess(true);
            testResult.setResponseTime(System.currentTimeMillis() - startTime);

            // 获取数据库版本信息
            DatabaseMetaData metaData = connection.getMetaData();
            testResult.setDatabaseVersion(metaData.getDatabaseProductName() + " " + metaData.getDatabaseProductVersion());
            testResult.setConnectionInfo("连接成功");

            // 获取表列表
            List<String> tables = new ArrayList<>();
            ResultSet rs = metaData.getTables(null, null, "%", new String[]{"TABLE"});
            int count = 0;
            while (rs.next() && count < 20) {
                tables.add(rs.getString("TABLE_NAME"));
                count++;
            }
            testResult.setAvailableTables(tables);

            log.info("数据库连接测试成功，响应时间：{}ms", testResult.getResponseTime());
            return Result.ok(testResult);

        } catch (Exception e) {
            log.error("JDBC连接测试失败", e);
            testResult.setSuccess(false);
            testResult.setErrorMessage(e.getMessage());
            testResult.setResponseTime(System.currentTimeMillis() - startTime);
            return Result.ok(testResult);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    log.warn("关闭数据库连接失败", e);
                }
            }
        }
    }

    /**
     * 测试MongoDB连接
     */
    private Result<DatabaseConnectionTestVo> testMongoConnection(DatabaseConnectionReq connectionReq,
                                                                DatabaseConnectionTestVo testResult,
                                                                long startTime) {
        MongoClient mongoClient = null;
        try {
            // 构建MongoDB连接字符串
            String connectionString = String.format("mongodb://%s:%s@%s:%d/%s",
                connectionReq.getUsername(),
                connectionReq.getPassword(),
                connectionReq.getHost(),
                connectionReq.getPort(),
                connectionReq.getDatabase()
            );

            mongoClient = MongoClients.create(connectionString);
            MongoDatabase database = mongoClient.getDatabase(connectionReq.getDatabase());

            // 测试连接
            database.runCommand(new Document("ping", 1));

            testResult.setSuccess(true);
            testResult.setResponseTime(System.currentTimeMillis() - startTime);
            testResult.setDatabaseVersion("MongoDB");
            testResult.setConnectionInfo("MongoDB连接成功");

            // 获取集合列表
            List<String> collections = new ArrayList<>();
            int count = 0;
            for (String collectionName : database.listCollectionNames()) {
                if (count < 20) {
                    collections.add(collectionName);
                    count++;
                } else {
                    break;
                }
            }
            testResult.setAvailableTables(collections);

            log.info("MongoDB连接测试成功，响应时间：{}ms", testResult.getResponseTime());
            return Result.ok(testResult);

        } catch (Exception e) {
            log.error("MongoDB连接测试失败", e);
            testResult.setSuccess(false);
            testResult.setErrorMessage(e.getMessage());
            testResult.setResponseTime(System.currentTimeMillis() - startTime);
            return Result.ok(testResult);
        } finally {
            if (mongoClient != null) {
                mongoClient.close();
            }
        }
    }

    /**
     * 创建同步任务对象
     */
    private DataSyncVo createSyncTask(String taskId, DataSyncReq syncReq) {
        DataSyncVo syncTask = new DataSyncVo();
        syncTask.setTaskId(taskId);
        syncTask.setStatus(1); // 准备中
        syncTask.setProgress(0);
        syncTask.setSourceDbType(syncReq.getSourceDatabase().getDbType());
        syncTask.setSourceHost(syncReq.getSourceDatabase().getHost() + ":" + syncReq.getSourceDatabase().getPort());
        syncTask.setSourceTable(syncReq.getSourceTable());
        syncTask.setTargetDatabase(syncReq.getTargetDatabase());
        syncTask.setTargetTable(syncReq.getTargetTable());
        syncTask.setTotalRecords(0L);
        syncTask.setSyncedRecords(0L);
        syncTask.setFailedRecords(0L);
        syncTask.setStartTime(new java.util.Date());
        syncTask.setCreatedTime(new java.util.Date());
        syncTask.setDescription(syncReq.getDescription());
        return syncTask;
    }

    /**
     * 执行数据同步的核心方法
     */
    @Async
    private void performDataSync(String taskId, DataSyncReq syncReq) {
        DataSyncVo syncTask = syncTaskCache.get(taskId);
        if (syncTask == null) {
            log.error("同步任务不存在: {}", taskId);
            return;
        }

        try {
            log.info("开始执行数据同步任务: {}", taskId);
            syncTask.setStatus(2); // 同步中

            // 根据数据库类型执行不同的同步逻辑
            if (syncReq.getSourceDatabase().getDbType() == 3) { // MongoDB
                syncFromMongoDB(syncTask, syncReq);
            } else { // MySQL 或 Oracle
                syncFromJdbcDatabase(syncTask, syncReq);
            }

            syncTask.setStatus(3); // 同步成功
            syncTask.setProgress(100);
            syncTask.setEndTime(new java.util.Date());

            log.info("数据同步任务完成: {}，同步记录数: {}", taskId, syncTask.getSyncedRecords());

        } catch (Exception e) {
            log.error("数据同步任务失败: " + taskId, e);
            syncTask.setStatus(4); // 同步失败
            syncTask.setErrorMessage(e.getMessage());
            syncTask.setEndTime(new java.util.Date());
        }
    }

    /**
     * 从JDBC数据库同步数据
     */
    private void syncFromJdbcDatabase(DataSyncVo syncTask, DataSyncReq syncReq) throws Exception {
        Connection sourceConnection = null;
        try {
            // 连接源数据库
            DatabaseConnectionReq sourceDb = syncReq.getSourceDatabase();
            Class.forName(sourceDb.getDriverClassName());
            sourceConnection = DriverManager.getConnection(
                sourceDb.getJdbcUrl(), sourceDb.getUsername(), sourceDb.getPassword()
            );

            // 创建目标数据库和表
            createTargetDatabaseAndTable(syncReq, sourceConnection);

            // 查询源数据总数
            String countSql = "SELECT COUNT(*) FROM " + syncReq.getSourceTable();
            PreparedStatement countStmt = sourceConnection.prepareStatement(countSql);
            ResultSet countRs = countStmt.executeQuery();
            countRs.next();
            long totalRecords = countRs.getLong(1);
            syncTask.setTotalRecords(totalRecords);

            // 分批同步数据
            int batchSize = syncReq.getBatchSize();
            long syncedRecords = 0;

            for (int offset = 0; offset < totalRecords; offset += batchSize) {
                String dataSql = "SELECT * FROM " + syncReq.getSourceTable() + " LIMIT " + batchSize + " OFFSET " + offset;
                PreparedStatement dataStmt = sourceConnection.prepareStatement(dataSql);
                ResultSet dataRs = dataStmt.executeQuery();

                // 批量插入目标表
                syncedRecords += insertBatchData(syncReq, dataRs);

                // 更新进度
                syncTask.setSyncedRecords(syncedRecords);
                syncTask.setProgress((int) (syncedRecords * 100 / totalRecords));

                log.info("同步进度: {}/{} ({}%)", syncedRecords, totalRecords, syncTask.getProgress());
            }

        } finally {
            if (sourceConnection != null) {
                sourceConnection.close();
            }
        }
    }

    /**
     * 从MongoDB同步数据
     */
    private void syncFromMongoDB(DataSyncVo syncTask, DataSyncReq syncReq) throws Exception {
        MongoClient mongoClient = null;
        try {
            // 连接MongoDB
            DatabaseConnectionReq sourceDb = syncReq.getSourceDatabase();
            String connectionString = String.format("mongodb://%s:%s@%s:%d/%s",
                sourceDb.getUsername(), sourceDb.getPassword(),
                sourceDb.getHost(), sourceDb.getPort(), sourceDb.getDatabase()
            );

            mongoClient = MongoClients.create(connectionString);
            MongoDatabase database = mongoClient.getDatabase(sourceDb.getDatabase());

            String collectionName = syncReq.getMongoConfig() != null &&
                                  !StringUtils.hasText(syncReq.getMongoConfig().getCollection()) ?
                                  syncReq.getSourceTable() : syncReq.getMongoConfig().getCollection();

            MongoCollection<Document> collection = database.getCollection(collectionName);

            // 创建目标表
            createTargetTableForMongo(syncReq);

            // 查询总数
            long totalRecords = collection.countDocuments();
            syncTask.setTotalRecords(totalRecords);

            // 分批同步数据
            int batchSize = syncReq.getBatchSize();
            long syncedRecords = 0;

            for (Document doc : collection.find()) {
                insertMongoDocument(syncReq, doc);
                syncedRecords++;

                if (syncedRecords % batchSize == 0) {
                    syncTask.setSyncedRecords(syncedRecords);
                    syncTask.setProgress((int) (syncedRecords * 100 / totalRecords));
                    log.info("MongoDB同步进度: {}/{} ({}%)", syncedRecords, totalRecords, syncTask.getProgress());
                }
            }

            syncTask.setSyncedRecords(syncedRecords);

        } finally {
            if (mongoClient != null) {
                mongoClient.close();
            }
        }
    }

    /**
     * 创建目标数据库和表
     */
    private void createTargetDatabaseAndTable(DataSyncReq syncReq, Connection sourceConnection) throws SQLException {
        // 创建目标数据库
        String createDbSql = "CREATE DATABASE IF NOT EXISTS `" + syncReq.getTargetDatabase() + "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        jdbcTemplate.execute(createDbSql);

        // 获取源表结构
        DatabaseMetaData metaData = sourceConnection.getMetaData();
        ResultSet columns = metaData.getColumns(null, null, syncReq.getSourceTable(), null);

        StringBuilder createTableSql = new StringBuilder();
        createTableSql.append("CREATE TABLE IF NOT EXISTS `")
                     .append(syncReq.getTargetDatabase())
                     .append("`.`")
                     .append(syncReq.getTargetTable())
                     .append("` (");

        boolean first = true;
        while (columns.next()) {
            if (!first) {
                createTableSql.append(", ");
            }

            String columnName = columns.getString("COLUMN_NAME");
            String dataType = columns.getString("TYPE_NAME");
            int columnSize = columns.getInt("COLUMN_SIZE");

            createTableSql.append("`").append(columnName).append("` ");

            // 映射数据类型
            if (dataType.equalsIgnoreCase("VARCHAR") || dataType.equalsIgnoreCase("VARCHAR2")) {
                createTableSql.append("VARCHAR(").append(columnSize).append(")");
            } else if (dataType.equalsIgnoreCase("TEXT") || dataType.equalsIgnoreCase("CLOB")) {
                createTableSql.append("TEXT");
            } else if (dataType.equalsIgnoreCase("INT") || dataType.equalsIgnoreCase("INTEGER")) {
                createTableSql.append("INT");
            } else if (dataType.equalsIgnoreCase("BIGINT")) {
                createTableSql.append("BIGINT");
            } else if (dataType.equalsIgnoreCase("DECIMAL") || dataType.equalsIgnoreCase("NUMBER")) {
                createTableSql.append("DECIMAL(").append(columnSize).append(",2)");
            } else if (dataType.equalsIgnoreCase("DATE") || dataType.equalsIgnoreCase("TIMESTAMP")) {
                createTableSql.append("DATETIME");
            } else {
                createTableSql.append("TEXT"); // 默认使用TEXT
            }

            first = false;
        }

        createTableSql.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        log.info("创建目标表SQL: {}", createTableSql.toString());
        jdbcTemplate.execute(createTableSql.toString());
    }

    /**
     * 为MongoDB创建目标表
     */
    private void createTargetTableForMongo(DataSyncReq syncReq) {
        // 创建目标数据库
        String createDbSql = "CREATE DATABASE IF NOT EXISTS `" + syncReq.getTargetDatabase() + "` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        jdbcTemplate.execute(createDbSql);

        // 为MongoDB创建简单的表结构
        StringBuilder createTableSql = new StringBuilder();
        createTableSql.append("CREATE TABLE IF NOT EXISTS `")
                     .append(syncReq.getTargetDatabase())
                     .append("`.`")
                     .append(syncReq.getTargetTable())
                     .append("` (")
                     .append("`id` VARCHAR(255) PRIMARY KEY, ")
                     .append("`document_data` JSON, ")
                     .append("`sync_time` DATETIME DEFAULT CURRENT_TIMESTAMP")
                     .append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        log.info("创建MongoDB目标表SQL: {}", createTableSql.toString());
        jdbcTemplate.execute(createTableSql.toString());
    }

    /**
     * 批量插入JDBC数据
     */
    private int insertBatchData(DataSyncReq syncReq, ResultSet resultSet) throws SQLException {
        int insertedCount = 0;
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        // 构建插入SQL
        StringBuilder insertSql = new StringBuilder();
        insertSql.append("INSERT INTO `")
                 .append(syncReq.getTargetDatabase())
                 .append("`.`")
                 .append(syncReq.getTargetTable())
                 .append("` (");

        // 添加列名
        for (int i = 1; i <= columnCount; i++) {
            if (i > 1) insertSql.append(", ");
            insertSql.append("`").append(metaData.getColumnName(i)).append("`");
        }
        insertSql.append(") VALUES (");

        // 添加占位符
        for (int i = 1; i <= columnCount; i++) {
            if (i > 1) insertSql.append(", ");
            insertSql.append("?");
        }
        insertSql.append(")");

        // 批量插入数据
        try (Connection connection = dataSource.getConnection();
             PreparedStatement pstmt = connection.prepareStatement(insertSql.toString())) {

            while (resultSet.next()) {
                for (int i = 1; i <= columnCount; i++) {
                    pstmt.setObject(i, resultSet.getObject(i));
                }
                pstmt.addBatch();
                insertedCount++;

                // 每1000条执行一次批量插入
                if (insertedCount % 1000 == 0) {
                    pstmt.executeBatch();
                    pstmt.clearBatch();
                }
            }

            // 执行剩余的批量插入
            if (insertedCount % 1000 != 0) {
                pstmt.executeBatch();
            }
        }

        return insertedCount;
    }

    /**
     * 插入MongoDB文档
     */
    private void insertMongoDocument(DataSyncReq syncReq, Document document) {
        String insertSql = "INSERT INTO `" + syncReq.getTargetDatabase() + "`.`" + syncReq.getTargetTable() +
                          "` (`id`, `document_data`, `sync_time`) VALUES (?, ?, NOW())";

        String documentId = document.getObjectId("_id") != null ?
                           document.getObjectId("_id").toString() :
                           UUID.randomUUID().toString();

        String jsonData = document.toJson();

        jdbcTemplate.update(insertSql, documentId, jsonData);
    }
}
