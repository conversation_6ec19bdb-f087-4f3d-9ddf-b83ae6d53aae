package com.hzw.heterogeneous.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzw.heterogeneous.controller.request.DataAccessCondition;
import com.hzw.heterogeneous.controller.response.DataAccessVo;
import com.hzw.heterogeneous.mapper.DataAccessMapper;
import com.hzw.heterogeneous.model.DataAccess;
import com.hzw.heterogeneous.service.DataAccessService;
import com.hzw.heterogeneous.util.Result;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Date;

/**
 * <p>
 * 数据接入 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Service
public class DataAccessServiceImpl extends ServiceImpl<DataAccessMapper, DataAccess> implements DataAccessService {

    @Value("${files.temporary.path}")
    private String tempFilePath;

    @Override
    public IPage<DataAccessVo> listPage(DataAccessCondition condition) {
        return this.baseMapper.listPage(condition.buildPage(), condition);
    }

    @Override
    public Result<Boolean> addDataAccess(MultipartFile file, String fileName,String fileType,String fileFormat,Integer dataType) {
        if (file.isEmpty()) {
            return Result.failed("文件不存在");
        }
        String originalFilename = file.getOriginalFilename();
        try {
//            Path filePath = Paths.get(tempFilePath, file.getOriginalFilename());
//            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            // 清理文件名，防止路径穿越攻击
            String safeFileName = sanitizeFileName(originalFilename);
            Path targetDir = Paths.get(tempFilePath);

            // 确保目录存在
            if (!Files.exists(targetDir)) {
                Files.createDirectories(targetDir);
            }

            Path filePath = targetDir.resolve(safeFileName);
            try (InputStream inputStream = file.getInputStream()) {
                Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
            }
            DataAccess dataAccess = new DataAccess();
            dataAccess.setDataType(dataType);
            dataAccess.setFileName(fileName);
            dataAccess.setFileType(fileType);
            dataAccess.setCreatedTime(new Date());
            dataAccess.setFileFormat(fileFormat);
            dataAccess.setFileUrl(filePath.toString());
            this.save(dataAccess);
        } catch (IOException e) {
            log.error("文件接入新增失败",e);
            return Result.failed("文件接入新增失败");
        }
        return Result.ok();
    }

    // 文件名清理函数，防止路径穿越
    private String sanitizeFileName(String filename) {
        if (filename == null || filename.isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        // 替换掉非法字符，例如 ../ 或 / 等
        return filename.replaceAll("[\\\\/]", "_");
    }
}
