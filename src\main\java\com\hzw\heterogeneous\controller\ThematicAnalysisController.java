package com.hzw.heterogeneous.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.hzw.heterogeneous.controller.response.*;
import com.hzw.heterogeneous.controller.response.HotTopicsVo;
import com.hzw.heterogeneous.controller.response.NewsVo;
import com.hzw.heterogeneous.model.Events;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import com.hzw.heterogeneous.service.HotTopicsService;
import com.hzw.heterogeneous.service.PersonService;
import com.hzw.heterogeneous.service.TopicEventSearchService;
import com.hzw.heterogeneous.service.WeaponService;
import com.hzw.heterogeneous.util.Paging;
import com.hzw.heterogeneous.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

@Api(tags = "专题分析")
@RestController
@RequestMapping("/thematicAnalysis")
public class ThematicAnalysisController {

    private static final Logger logger = LoggerFactory.getLogger(ThematicAnalysisController.class);

    @Autowired
    private TopicEventSearchService topicEventSearchService;

    @Autowired
    private HotTopicsService hotTopicsService;

    @Autowired
    private PersonService personService;

    @Autowired
    private WeaponService weaponService;

    @ApiOperation(value = "主题事件")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping(value = "/listTopicEventPage")
    public Result<Paging<TopicEventSearchVo>> listTopicEventPage(@RequestBody ThematicAnalysisCondition condition) {
        return Result.ok(topicEventSearchService.listPage(condition));
    }

    @ApiOperation(value = "热点资讯")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping(value = "/listHotNewsPage")
    public Result<Paging<HotNewsVo>> listHotNewsPage(@RequestBody ThematicAnalysisCondition condition) {
        return Result.ok(hotTopicsService.listHotNewsPage(condition));
    }

    @ApiOperation(value = "重要人物类型数量")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping(value = "/listPersonNum")
    public Result<List<EntityTypeNumVo>> listPersonNum(@RequestBody ThematicAnalysisCondition condition) {
        return Result.ok(personService.listPersonNum(condition));
    }

    @ApiOperation(value = "重要人物", notes = "以人物为实体单位，支持多张图片和视频的展示")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping(value = "/listPersonPage")
    public Result<Paging<PersonVo>> listPersonPage(@RequestBody ThematicAnalysisCondition condition) {
        return Result.ok(personService.listPersonPage(condition));
    }

    @ApiOperation(value = "重要人物详情", notes = "获取人物详细信息，包括多张图片、视频和动向信息")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping(value = "/getPersonInfo")
    public Result<PersonVo> getPersonInfo(@RequestBody ThematicAnalysisCondition condition) {
        logger.info("收到获取人物详情请求，参数: {}", condition);
        
        try {
            // 参数验证
            if(null == condition || null == condition.getId() || condition.getId().trim().isEmpty()){
                logger.warn("获取人物详情失败：参数不合法，condition: {}", condition);
                return Result.failed("请选择正确的数据进行操作");
            }
            
            logger.debug("开始获取人物详情，人物ID: {}", condition.getId());
            PersonVo result = personService.getPersonInfo(condition);
            
            if (result == null) {
                logger.warn("未找到指定人物信息，人物ID: {}", condition.getId());
                return Result.failed("未找到指定的人物信息");
            }
            
            logger.info("成功获取人物详情，人物ID: {}, 姓名: {}", condition.getId(), result.getUserName());
            return Result.ok(result);
        } catch (Exception e) {
            logger.error("获取人物详情异常，人物ID: {}", condition != null ? condition.getId() : "null", e);
            return Result.failed("获取人物详情失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "目标实体类型以及数量")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping(value = "/listWeaponNum")
    public Result<List<EntityTypeNumVo>> listWeaponNum(@RequestBody ThematicAnalysisCondition condition) {
        return Result.ok(weaponService.listWeaponNum(condition));
    }

    @ApiOperation(value = "目标实体")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping(value = "/listWeaponPage")
    public Result<Paging<WeaponVo>> listWeaponPage(@RequestBody ThematicAnalysisCondition condition) {
        return Result.ok(weaponService.listWeaponPage(condition));
    }

    @ApiOperation(value = "目标实体详情")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping(value = "/getWeaponInfo")
    public Result<WeaponVo> getWeaponInfo(@RequestBody ThematicAnalysisCondition condition) {
        if(null == condition.getId()){
            return Result.failed("请选择正确的数据进行操作");
        }
        return Result.ok(weaponService.getWeaponInfo(condition));
    }

    @ApiOperation(value = "导出主题事件")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping("/exportTopicEvent")
    public void exportTopicEvent(HttpServletResponse response, @RequestBody ThematicAnalysisCondition condition) {
        List<TopicEventSearchVo> list = topicEventSearchService.exportList(condition.getIds());
        //设置标题信息
        Workbook workbook;
        String title = "主题事件";
        workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"主题事件"), TopicEventSearchVo.class,list);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("主题事件.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导出热点资讯")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping("/exportHotNews")
    public void exportHotNews(HttpServletResponse response, @RequestBody ThematicAnalysisCondition condition) {
        List<HotNewsVo> list = hotTopicsService.exportList(condition.getIds());
        //设置标题信息
        Workbook workbook;
        String title = "热点资讯";
        workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"热点资讯"), HotNewsVo.class,list);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("热点资讯.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "导出重要人物")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping("/exportPersons")
    public void exportPersons(HttpServletResponse response, @RequestBody ThematicAnalysisCondition condition) {
        List<PersonVo> list = personService.exportList(condition.getIds());
        //设置标题信息
        Workbook workbook;
        String title = "重要人物";
        workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"重要人物"), PersonVo.class,list);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("重要人物.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "导出目标实体")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping("/exportWeapon")
    public void exportWeapon(HttpServletResponse response, @RequestBody ThematicAnalysisCondition condition) {
        List<WeaponVo> list = weaponService.exportList(condition.getIds());
        //设置标题信息
        Workbook workbook;
        String title = "目标实体";
        workbook= ExcelExportUtil.exportExcel(new ExportParams(title,"目标实体"), WeaponVo.class,list);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("目标实体.xls", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        try {
            workbook.write(response.getOutputStream());
            response.getOutputStream().close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "主题事件")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @GetMapping(value = "/getTopicEventInfo/{id}")
    public Result<TopicEventSearchVo> getTopicEventInfo(@PathVariable String id) {
        return Result.ok(topicEventSearchService.getTopicEventInfo(id));
    }

    @ApiOperation(value = "热点资讯")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @GetMapping(value = "/getHotNewsInfo/{id}")
    public Result<HotNewsVo> getHotNewsInfo(@PathVariable String id) {
        return Result.ok(hotTopicsService.getHotNewsInfo(id));
    }

    @ApiOperation(value = "根据热点话题ID查询关联新闻列表")
    @ApiImplicitParam(name = "condition", value = "分页查询条件", required = true, dataType = "ThematicAnalysisCondition", paramType = "body")
    @PostMapping(value = "/listRelatedNewsByHotTopicsId/{hotTopicsId}")
    public Result<Paging<NewsVo>> listRelatedNewsByHotTopicsId(
            @PathVariable String hotTopicsId, 
            @RequestBody ThematicAnalysisCondition condition) {
        return Result.ok(hotTopicsService.listRelatedNewsByHotTopicsId(hotTopicsId, condition));
    }

    @ApiOperation(value = "根据动向ID查询关联事件列表", notes = "获取指定动向的所有关联事件，按时间先后顺序排序")
    @ApiImplicitParam(name = "trendId", value = "动向ID", required = true, dataType = "String", paramType = "path")
    @GetMapping(value = "/getTrendRelatedEvents/{trendId}")
    public Result<List<Events>> getTrendRelatedEvents(@PathVariable String trendId) {
        return Result.ok(personService.getTrendRelatedEvents(trendId));
    }

}
