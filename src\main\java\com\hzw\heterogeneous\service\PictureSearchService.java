package com.hzw.heterogeneous.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.request.PictureSearchCondition;
import com.hzw.heterogeneous.controller.response.PictureSearchVo;
import org.springframework.web.multipart.MultipartFile;



/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2024-11-11
 */
public interface PictureSearchService {

    /**
     * 分页查询文本数据
     * @param condition
     * @return
     */
    IPage<PictureSearchVo> listPage(PictureSearchCondition condition);

    String getBase64(String url);

    public  IPage<PictureSearchVo> uploadImageAndCallApi(MultipartFile file);
}
