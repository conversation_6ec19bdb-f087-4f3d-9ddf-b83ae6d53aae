package com.hzw.heterogeneous.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.response.HotNewsVo;
import com.hzw.heterogeneous.controller.response.HotTopicsVo;
import com.hzw.heterogeneous.controller.response.NewsVo;
import com.hzw.heterogeneous.mapper.HotTopicsMapper;
import com.hzw.heterogeneous.model.HotTopics;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import com.hzw.heterogeneous.service.HotTopicsService;
import com.hzw.heterogeneous.util.Paging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* HotTopicsServiceImpl实现类 - 热点话题业务逻辑实现
* 对应数据库表: t_hot_topics
*/
@Slf4j
@Service
public class HotTopicsServiceImpl implements HotTopicsService {

    @Autowired
    private HotTopicsMapper hotTopicsMapper;

    /**
     * 分页查询热点话题
     * @param condition 查询条件
     * @return 分页结果
     */
    @Override
    public Paging<HotNewsVo> listHotNewsPage(ThematicAnalysisCondition condition) {
        log.debug("开始分页查询热点话题，条件：{}", condition);
        
        try {
            // 直接使用MyBatis Plus分页查询，支持MySQL全文索引
            IPage<HotNewsVo> page = condition.buildPage();
            page = hotTopicsMapper.listPage(page, condition);
            
            log.debug("热点话题分页查询完成，共查询到{}条记录", page.getTotal());
            return Paging.buildPaging(page);
            
        } catch (Exception e) {
            log.error("热点话题分页查询失败，条件：{}", condition, e);
            throw new RuntimeException("热点话题查询失败", e);
        }
    }

    /**
     * 根据ID列表导出热点话题数据
     * @param ids ID列表
     * @return 导出数据列表
     */
    @Override
    public List<HotNewsVo> exportList(List<String> ids) {
        log.debug("开始导出热点话题数据，ID列表：{}", ids);
        if (ids == null || ids.isEmpty()) {
            log.warn("导出热点话题数据失败：ID列表为空");
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<HotTopics> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HotTopics::getId, ids);
        // 注意：t_hot_topics表中不存在isDelete字段，不需要过滤删除状态
        List<HotTopics> hotTopicsList = hotTopicsMapper.selectList(queryWrapper);
        
        List<HotNewsVo> hotNewsVoList = new ArrayList<>();
        for (HotTopics hotTopics : hotTopicsList) {
            HotNewsVo hotNewsVo = new HotNewsVo();
            BeanUtils.copyProperties(hotTopics, hotNewsVo);
            hotNewsVoList.add(hotNewsVo);
        }
        
        log.debug("热点话题数据导出完成，共导出{}条记录", hotNewsVoList.size());
        return hotNewsVoList;
    }

    /**
     * 根据ID获取热点话题详情
     * @param id 热点话题ID
     * @return 热点话题详情
     */
    @Override
    public HotNewsVo getHotNewsInfo(String id) {
        log.debug("开始获取热点话题详情，ID：{}", id);
        if (id == null || id.trim().isEmpty()) {
            log.warn("获取热点话题详情失败：ID为空");
            return null;
        }
        
        HotTopics hotTopics = hotTopicsMapper.selectById(id);
        if (hotTopics == null) {
            log.warn("未找到ID为{}的热点话题", id);
            return null;
        }
        
        log.debug("热点话题详情获取完成，标题：{}", hotTopics.getTitle());
        return BeanUtil.copyProperties(hotTopics, HotNewsVo.class);
    }

    /**
     * 分页查询热点话题 (推荐使用)
     * @param condition 查询条件
     * @return 分页结果
     */
    @Override
    public Paging<HotTopicsVo> listHotTopicsPage(ThematicAnalysisCondition condition) {
        log.debug("开始分页查询热点话题(新VO)，条件：{}", condition);
        
        // 先调用原有的分页查询方法
        Paging<HotNewsVo> originalResult = this.listHotNewsPage(condition);
        
        // 转换为新的VO格式
        List<HotTopicsVo> hotTopicsVoList = new ArrayList<>();
        for (HotNewsVo hotNewsVo : originalResult.getRecords()) {
            HotTopicsVo hotTopicsVo = BeanUtil.copyProperties(hotNewsVo, HotTopicsVo.class);
            hotTopicsVoList.add(hotTopicsVo);
        }
        
        // 使用原分页信息创建新的分页结果
        Paging<HotTopicsVo> result = new Paging<>(
            originalResult.getPageSize(),
            originalResult.getPage(),
            originalResult.getTotal(),
            originalResult.getTotalPage(),
            hotTopicsVoList
        );
        
        log.debug("热点话题分页查询完成(新VO)，共查询到{}条记录", result.getTotal());
        return result;
    }

    /**
     * 根据ID列表导出热点话题数据 (推荐使用)
     * @param ids ID列表
     * @return 导出数据列表
     */
    @Override
    public List<HotTopicsVo> exportHotTopicsList(List<String> ids) {
        log.debug("开始导出热点话题数据(新VO)，ID列表：{}", ids);
        if (ids == null || ids.isEmpty()) {
            log.warn("导出热点话题数据失败：ID列表为空");
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<HotTopics> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HotTopics::getId, ids);
        List<HotTopics> hotTopicsList = hotTopicsMapper.selectList(queryWrapper);
        
        List<HotTopicsVo> hotTopicsVoList = new ArrayList<>();
        for (HotTopics hotTopics : hotTopicsList) {
            HotTopicsVo hotTopicsVo = BeanUtil.copyProperties(hotTopics, HotTopicsVo.class);
            hotTopicsVoList.add(hotTopicsVo);
        }
        
        log.debug("热点话题数据导出完成(新VO)，共导出{}条记录", hotTopicsVoList.size());
        return hotTopicsVoList;
    }

    /**
     * 根据ID获取热点话题详情 (推荐使用)
     * @param id 热点话题ID
     * @return 热点话题详情
     */
    @Override
    public HotTopicsVo getHotTopicsInfo(String id) {
        log.debug("开始获取热点话题详情(新VO)，ID：{}", id);
        if (id == null || id.trim().isEmpty()) {
            log.warn("获取热点话题详情失败：ID为空");
            return null;
        }
        
        HotTopics hotTopics = hotTopicsMapper.selectById(id);
        if (hotTopics == null) {
            log.warn("未找到ID为{}的热点话题", id);
            return null;
        }
        
        log.debug("热点话题详情获取完成(新VO)，标题：{}", hotTopics.getTitle());
        return BeanUtil.copyProperties(hotTopics, HotTopicsVo.class);
    }

    /**
     * 根据热点话题ID查询关联的新闻列表
     * @param hotTopicsId 热点话题ID
     * @param condition 分页条件
     * @return 关联新闻列表
     */
    @Override
    public Paging<NewsVo> listRelatedNewsByHotTopicsId(String hotTopicsId, ThematicAnalysisCondition condition) {
        log.debug("开始查询热点话题关联新闻，热点话题ID：{}", hotTopicsId);
        
        if (hotTopicsId == null || hotTopicsId.trim().isEmpty()) {
            log.warn("查询热点话题关联新闻失败：热点话题ID为空");
            IPage<NewsVo> emptyPage = condition.buildPage();
            emptyPage.setTotal(0);
            emptyPage.setRecords(new ArrayList<>());
            return Paging.buildPaging(emptyPage);
        }
        
        try {
            // 使用分页查询关联新闻，按时间排序
            IPage<NewsVo> page = condition.buildPage();
            page = hotTopicsMapper.listRelatedNewsByHotTopicsId(page, hotTopicsId);
            
            log.debug("热点话题关联新闻查询完成，共查询到{}条记录", page.getTotal());
            return Paging.buildPaging(page);
            
        } catch (Exception e) {
            log.error("热点话题关联新闻查询失败，热点话题ID：{}", hotTopicsId, e);
            throw new RuntimeException("热点话题关联新闻查询失败", e);
        }
    }
}