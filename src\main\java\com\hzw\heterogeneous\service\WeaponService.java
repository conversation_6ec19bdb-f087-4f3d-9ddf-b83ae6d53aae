package com.hzw.heterogeneous.service;

import com.hzw.heterogeneous.controller.response.EntityTypeNumVo;
import com.hzw.heterogeneous.controller.response.WeaponVo;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import com.hzw.heterogeneous.util.Paging;

import java.util.List;

public interface WeaponService {
    /**
     * 目标实体类型以及数量
     * @param condition
     * @return
     */
    List<EntityTypeNumVo> listWeaponNum(ThematicAnalysisCondition condition);

    /**
     * 分页查询目标实体
     * @param condition
     * @return
     */
    Paging<WeaponVo> listWeaponPage(ThematicAnalysisCondition condition);

    /**
     * 获取目标实体详情
     * @param condition
     * @return
     */
    WeaponVo getWeaponInfo(ThematicAnalysisCondition condition);

    List<WeaponVo> exportList(List<String> ids);
}
