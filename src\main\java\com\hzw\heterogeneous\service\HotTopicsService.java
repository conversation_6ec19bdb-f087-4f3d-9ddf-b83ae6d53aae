package com.hzw.heterogeneous.service;

import com.hzw.heterogeneous.controller.response.HotNewsVo;
import com.hzw.heterogeneous.controller.response.HotTopicsVo;
import com.hzw.heterogeneous.controller.response.NewsVo;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import com.hzw.heterogeneous.util.Paging;

import java.util.List;

/**
* HotTopicsService接口 - 热点话题业务逻辑层
* 对应数据库表: t_hot_topics
*/
public interface HotTopicsService {

    /**
     * 分页查询热点话题 (兼容性方法)
     * @param condition 查询条件
     * @return 分页结果
     */
    Paging<HotNewsVo> listHotNewsPage(ThematicAnalysisCondition condition);

    /**
     * 分页查询热点话题 (推荐使用)
     * @param condition 查询条件
     * @return 分页结果
     */
    Paging<HotTopicsVo> listHotTopicsPage(ThematicAnalysisCondition condition);

    /**
     * 根据ID列表导出热点话题数据 (兼容性方法)
     * @param ids ID列表
     * @return 导出数据列表
     */
    List<HotNewsVo> exportList(List<String> ids);

    /**
     * 根据ID列表导出热点话题数据 (推荐使用)
     * @param ids ID列表
     * @return 导出数据列表
     */
    List<HotTopicsVo> exportHotTopicsList(List<String> ids);

    /**
     * 根据ID获取热点话题详情 (兼容性方法)
     * @param id 热点话题ID
     * @return 热点话题详情
     */
    HotNewsVo getHotNewsInfo(String id);

    /**
     * 根据ID获取热点话题详情 (推荐使用)
     * @param id 热点话题ID
     * @return 热点话题详情
     */
    HotTopicsVo getHotTopicsInfo(String id);

    /**
     * 根据热点话题ID查询关联的新闻列表
     * @param hotTopicsId 热点话题ID
     * @return 关联新闻列表
     */
    Paging<NewsVo> listRelatedNewsByHotTopicsId(String hotTopicsId, ThematicAnalysisCondition condition);
}