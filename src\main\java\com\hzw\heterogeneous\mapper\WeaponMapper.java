package com.hzw.heterogeneous.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.response.EntityTypeNumVo;
import com.hzw.heterogeneous.controller.response.WeaponVo;
import com.hzw.heterogeneous.model.Weapon;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WeaponMapper extends BaseMapper<Weapon> {

    /**
     * 目标实体类型以及数量
     * @param
     * @return
     */
    List<EntityTypeNumVo> listWeaponNum();

    IPage<WeaponVo> listWeaponPage(@Param("page")IPage<WeaponVo> page, @Param("condition") ThematicAnalysisCondition condition);

    WeaponVo getWeaponInfo(@Param("id")String id);

    List<Weapon> selectByPaths(@Param("paths") List<String> paths);
    
    /**
     * 根据关键词模糊查询武器
     * @param keyword 搜索关键词
     * @param limit 结果数量限制
     * @return 武器列表
     */
    List<Weapon> searchWeaponsByKeyword(@Param("keyword") String keyword, @Param("limit") Integer limit);
}
