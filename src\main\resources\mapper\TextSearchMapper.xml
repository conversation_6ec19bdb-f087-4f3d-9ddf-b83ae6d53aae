<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzw.heterogeneous.mapper.TextSearchMapper">

    <!-- 分页检索文献信息 -->
    <select id="literatureListPage" resultType="com.hzw.heterogeneous.controller.response.TextSearchVo">
        SELECT
        id,
        title,
        summary,
        file_content as content,
        source,
        tags,
        key_tags as keyTags,
        (0
        <if test="null != condition.keyWordsParam and '' != condition.keyWordsParam ">
            + MATCH(title,summary) AGAINST (#{condition.keyWordsParam} IN BOOLEAN MODE)
        </if>) as relevance,
        date_format(time,'%Y-%m-%d %H:%i:%s') as createdTime
        FROM t_literature
        where 1=1
        <if test="null != condition.keyWordsParam and '' != condition.keyWordsParam ">
            and MATCH(title,summary) AGAINST (#{condition.keyWordsParam} IN BOOLEAN MODE)
        </if>

        <if test="null != condition.startTime and '' != condition.startTime ">
            AND	date_format(time,'%Y-%m-%d %H:%i:%s') &gt;= #{condition.startTime}
        </if>
        <if test="null != condition.endTime and '' != condition.endTime ">
            AND date_format(time,'%Y-%m-%d %H:%i:%s') &lt;= #{condition.endTime}
        </if>
        <if test="null == condition.orderType and condition.orderType == 1">
            ORDER BY relevance DESC
        </if>
        <if test="null != condition.orderType and condition.orderType == 2">
            ORDER BY time DESC
        </if>
    </select>

    <!-- 分页检索新闻信息 -->
    <select id="newsListPage" resultType="com.hzw.heterogeneous.controller.response.TextSearchVo">
        SELECT
        id,
        title,
        '' summary,
        content,
        source,
        tags,
        key_tags as keyTags,
        (0
        <if test="null != condition.keyWordsParam and '' != condition.keyWordsParam ">
            + MATCH(title,content) AGAINST (#{condition.keyWordsParam} IN BOOLEAN MODE)
        </if>) as relevance,
        date_format(time,'%Y-%m-%d %H:%i:%s') as createdTime
        FROM t_news
        where 1=1
        <if test="null != condition.keyWordsParam and '' != condition.keyWordsParam ">
            and MATCH(title,content) AGAINST (#{condition.keyWordsParam} IN BOOLEAN MODE)
        </if>
<!--        <if test="null != condition.keyWords1 and '' != condition.keyWords1 ">-->
<!--            AND	title  like concat('%',#{condition.keyWords1},'%')-->
<!--        </if>-->
<!--        <if test="null != condition.keyWords2 and '' != condition.keyWords2 ">-->
<!--            AND	summary  like concat('%',#{condition.keyWords2},'%')-->
<!--        </if>-->
<!--        <if test="null != condition.keyWords3 and '' != condition.keyWords3 ">-->
<!--            AND	content  like concat('%',#{condition.keyWords3},'%')-->
<!--        </if>-->
        <if test="null != condition.startTime and '' != condition.startTime ">
            AND	date_format(time,'%Y-%m-%d %H:%i:%s') &gt;= #{condition.startTime}
        </if>
        <if test="null != condition.endTime and '' != condition.endTime ">
            AND date_format(time,'%Y-%m-%d %H:%i:%s') &lt;= #{condition.endTime}
        </if>
        <if test="null == condition.orderType and condition.orderType == 1">
            ORDER BY relevance DESC
        </if>
        <if test="null != condition.orderType and condition.orderType == 2">
            ORDER BY time DESC
        </if>
    </select>

    <!-- 查询实体信息 -->
    <select id="queryEntityByTags" resultType="com.hzw.heterogeneous.controller.response.EntitySearchVo">
        SELECT t.id,t.name,t.url,t.type,t.detail,t.relevance FROM(
        SELECT
            t1.id,
            t1.user_name AS name,
            t3.url AS url,
            '人物' AS type,
            CONCAT_WS(',', t1.user_name, t1.country,  t1.position, t1.resume) AS detail,
            MATCH(t1.tags) AGAINST (#{tags} IN BOOLEAN MODE) AS relevance
        FROM t_person t1
        LEFT JOIN t_person_weapon_picture_rel t2 ON t1.id = t2.person_weapon_id and t2.picture_type = 1
        LEFT JOIN t_picture t3 ON t2.picture_id = t3.id
        WHERE MATCH(t1.tags) AGAINST (#{tags} IN BOOLEAN MODE)
        UNION
        SELECT
            t1.id,
            t1.name,
            t3.url AS url,
            '武器' AS type,
            CONCAT_WS(',', t1.name,t1.code, t1.param) AS detail,
            MATCH(t1.tags) AGAINST (#{tags} IN BOOLEAN MODE) AS relevance
        FROM t_weapon t1
            LEFT JOIN t_person_weapon_picture_rel t2 ON t1.id = t2.person_weapon_id and t2.picture_type = 2
            LEFT JOIN t_picture t3 ON t2.picture_id = t3.id
        WHERE MATCH(t1.tags) AGAINST (#{tags} IN BOOLEAN MODE)) t
        ORDER BY relevance DESC LIMIT 10
    </select>
    <select id="getNewsById" resultType="com.hzw.heterogeneous.controller.response.TextSearchVo">
        SELECT
        id,
        title,
       '' summary,
        content,
        source,
        tags,
        key_tags as keyTags,
        event_extract as eventExtract,
        date_format(time,'%Y-%m-%d %H:%i:%s') as createdTime
        FROM t_news
        where id = #{id}

    </select>
    <select id="getLiteratureById" resultType="com.hzw.heterogeneous.controller.response.TextSearchVo">
        SELECT
        id,
        title,
        summary,
        file_content as content,
        source,
        tags,
        file_path as filePath,
        key_tags as keyTags,
        date_format(time,'%Y-%m-%d %H:%i:%s') as createdTime
        FROM t_literature
        where id = #{id}

    </select>

</mapper>
