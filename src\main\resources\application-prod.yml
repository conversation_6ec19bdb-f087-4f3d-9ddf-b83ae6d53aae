server:
  port: 8080
  servlet:
    context-path: /heterogeneous
  max-http-header-size: 100000

#datasource
spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  #  application:
  #    name: heterogeneous-api
  
  # 标准数据源配置（替代ShardingSphere）
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************************
    username: root
    password: qmx123456
    
    # Druid连接池配置
    druid:
      # 初始化连接数量
      initial-size: 10
      # 最小连接数量
      min-idle: 10
      # 最大连接数量
      max-active: 50
      # 最大等待时间
      max-wait: 60000
      # 间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 用来检测连接是否有效的sql
      validation-query: SELECT 1
      # 建议配置为true，不影响性能，并且保证安全性
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  jackson:
    # 全局设置@JsonFormat的格式pattern
    date-format: yyyy-MM-dd HH:mm:ss
    # 当地时区
    locale: zh
    # 设置全局时区
    time-zone: GMT+8
    # 常用，全局设置pojo或被@JsonInclude注解的属性的序列化方式
    #    default-property-inclusion: non_null #不为空的属性才会序列化,具体属性可看JsonInclude.Include
    # 常规默认,枚举类SerializationFeature中的枚举属性为key，值为boolean设置jackson序列化特性,具体key请看SerializationFeature源码
    serialization:
      write-dates-as-timestamps: false # 返回的java.util.date转换成timestamp
      FAIL_ON_EMPTY_BEANS: true # 对象为空时是否报错，默认true
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 26379
    # 数据库索引
    database: 4
    # 密码
    password: TR0Y9s27Oda6oqKs
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  rabbitmq:
    # 地址
    host: **************
    # 端口
    port: 25672
    # 账号
    username: hzw
    # 密码
    password: TR0Y9s27Oda6oqKs
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 2000
        default-requeue-rejected: false
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    throw-exception-if-no-handler-found: true
  servlet:
    multipart:
      # 开启 multipart 上传功能
      enabled: true
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 最大文件大小
      max-file-size: 200MB
      # 最大请求大小
      max-request-size: 215MB
  resources:
    add-mappings: true
mybatis-plus:
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
  configuration:
    map-underscore-to-camel-case: true
    typeAliasesPackage: com.hzw.heterogeneous.**.model
    mapper-locations: mapper/*Mapper.xml
    config-location: mybatis/mybatis-config.xml
    #logic-delete-field: isDelete  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
    logic-delete-value: 1 # 逻辑已删除值(默认为 1)
    logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
sa-token:
  token-name: Authorization
  # token前缀 在前端代码中需要加入该前缀，具体可看后文
  token-prefix: Bearer
  # 是否从header中读取token
  is-read-header: true
  sign:
    # API 接口签名秘钥 （随便乱摁几个字母即可）
    secret-key: kQwIOrYvnXmSDkwEiFngrKidMcdrgKoR

#log
logging:
  # 过滤开关
  enabled: true
  level:
    root: INFO
    com:
      hzw:
        bid: trace
  file:
    path: /log

swagger:
  enable: true
  title: JTCC-open-API
  description: JTCC-open-API
  urlPrefix: http://
  termsOfServiceUrl: /doc.html
  version: 1.0
  local: true


files:
  template:
    path: /sunflower/template/
  temporary:
    path: /sunflower/temporary/
  sign:
    path: /data/esign/


api:
  image:
    match:
      url: http://im.halusoft.com:28085/api/image_match
      tmpDirPath: /sunflower/temporary/tmpDirPath
      cleanup:
        enable: true
        days: 3
        cron: 0 0 2 * * ?