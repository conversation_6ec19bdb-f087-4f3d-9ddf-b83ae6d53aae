package com.hzw.heterogeneous.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.request.PictureSearchCondition;
import com.hzw.heterogeneous.controller.response.PictureSearchVo;
import com.hzw.heterogeneous.controller.response.TextSearchVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> Gen
 * @since 2024-05-14
 */
public interface PictureSearchMapper {

    /**
     * 分页检索图片信息
     * @param page
     * @param condition
     * @return
     */
    IPage<PictureSearchVo> listPicturePage(@Param("page") IPage<TextSearchVo> page, @Param("condition") PictureSearchCondition condition);

    IPage<PictureSearchVo> listPicturePageByPerson(@Param("page")IPage<PictureSearchVo> page, @Param("condition")PictureSearchCondition condition);

    IPage<PictureSearchVo> listPicturePageByWeapon(@Param("page")IPage<PictureSearchVo> page, @Param("condition")PictureSearchCondition condition);

}
