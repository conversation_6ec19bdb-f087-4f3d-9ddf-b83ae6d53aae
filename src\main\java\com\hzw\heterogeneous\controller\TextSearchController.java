package com.hzw.heterogeneous.controller;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.request.EntitySearchReq;
import com.hzw.heterogeneous.controller.request.TextSearchCondition;
import com.hzw.heterogeneous.controller.response.ComprehensiveSearchVo;
import com.hzw.heterogeneous.controller.response.EntitySearchVo;
import com.hzw.heterogeneous.controller.response.RecommendationVo;
import com.hzw.heterogeneous.controller.response.TextSearchVo;
import com.hzw.heterogeneous.service.TextSearchService;
import com.hzw.heterogeneous.util.Paging;
import com.hzw.heterogeneous.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2024/11/11 9:06
 * @description: 文本检索服务
 * @version: 1.0
 */
@Api(tags = "文本检索")
@RestController
@RequestMapping("/textSearch")
public class TextSearchController {

    @Autowired
    private TextSearchService textSearchService;

    @ApiOperation(value = "分页查询文本数据")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "TextSearchCondition", paramType = "body")
    @PostMapping(value = "/listPage")
    public Result<Paging<TextSearchVo>> listPage(@RequestBody TextSearchCondition condition) {
        IPage<TextSearchVo> page = textSearchService.listPage(condition);
        return Result.ok(Paging.buildPaging(page));
    }

    @ApiOperation(value = "根据实体Id查询实体详细信息")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "EntitySearchReq", paramType = "body")
    @PostMapping(value = "/queryEntityByTags")
    public Result<List<EntitySearchVo>> queryEntityByTags(@RequestBody EntitySearchReq req) {
        return textSearchService.queryEntityByTags(req);
    }


    @ApiOperation(value = "分页查询文本数据")
    @ApiImplicitParam(name = "condition", value = "查询条件", required = true, dataType = "TextSearchCondition", paramType = "body")
    @PostMapping(value = "/queryEntityById")
    public Result<TextSearchVo> queryEntityById(@RequestBody TextSearchCondition condition) {
        TextSearchVo searchVo = textSearchService.queryEntityById(condition);
        return Result.ok(searchVo);
    }
    
    @ApiOperation(value = "获取推荐列表", notes = "根据关键词模糊匹配人物实体、武器实体的名称，并形成推荐列表")
    @ApiImplicitParam(name = "keyword", value = "搜索关键词", required = true, dataType = "String", paramType = "query")
    @GetMapping(value = "/recommendation")
    public Result<RecommendationVo> getRecommendationList(
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit) {
        RecommendationVo result = textSearchService.getRecommendationList(keyword, limit);
        return Result.ok(result);
    }
}
