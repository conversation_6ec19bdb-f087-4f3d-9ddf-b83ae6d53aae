CREATE TABLE `t_video` (
                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                           `type` int DEFAULT NULL COMMENT '类型1人物2武器',
                           `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '名称',
                           `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '地址',
                           `tags` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '标签',
                           `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                           `z_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                           `oid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                           PRIMARY KEY (`id`) USING BTREE,
                           FULLTEXT KEY `idx_fulltext_name_tags` (`name`,`tags`) /*!50100 WITH PARSER `ngram` */
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3 COMMENT='视频表';


CREATE TABLE `t_person_weapon_video_rel` (
                                                  `id` bigint NOT NULL AUTO_INCREMENT,
                                                  `persion_weapon_id` bigint DEFAULT NULL COMMENT '人物/武器库id',
                                                  `type` varchar(255) COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '人物视频，武器视频',
                                                  `video_id` bigint DEFAULT NULL COMMENT '视频库id',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin;

CREATE TABLE `t_trend_event_relation` (
                                          `id` bigint NOT NULL AUTO_INCREMENT,
                                          `trend_id` bigint DEFAULT NULL COMMENT '动向ID',
                                          `event_id` bigint DEFAULT NULL COMMENT '事件ID',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin COMMENT='动向与事件对应关系';