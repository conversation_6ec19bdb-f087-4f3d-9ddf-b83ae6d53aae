package com.hzw.heterogeneous.controller;

import com.hzw.heterogeneous.controller.request.ComprehensiveSearchReq;
import com.hzw.heterogeneous.controller.response.ComprehensiveSearchVo;
import com.hzw.heterogeneous.model.SearchLog;
import com.hzw.heterogeneous.service.SearchLogService;
import com.hzw.heterogeneous.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "检索记录")
@Slf4j
@RestController
@RequestMapping("/searchLog")
public class SearchLogController {

    @Autowired
    private SearchLogService searchLogService;

//    @ApiOperation(value = "搜索记录列表")
//    @PostMapping(value = "/listSearchLog")
//    public Result<List<SearchLog>> listSearchLog(@RequestBody SearchLog log) {
//        return Result.ok(searchLogService.listSearchLog(log.getKeyWords(), log.getPageType()));
//    }

    @ApiOperation(value = "新增搜索记录")
    @PostMapping(value = "/addSearchLog")
    public Result<Boolean> addSearchLog(@RequestBody SearchLog searchLog) {
        return Result.ok(searchLogService.saveSearchLog(searchLog));
    }

    @ApiOperation(value = "综合关键词搜索", notes = "根据关键词模糊搜索日志、人物和武器名称，返回统一格式数组：type 1-日志，2-武器，3-人物")
    @PostMapping(value = "/listSearchLog")
    public Result<List<ComprehensiveSearchVo.SearchResultItem>> comprehensiveSearch(@RequestBody ComprehensiveSearchReq request) {
        log.info("接收综合搜索请求，关键词：{}，页面类型：{}，限制数量：{}", 
                request.getKeyWords(), request.getPageType(), request.getLimit());
        
        // 参数验证
        if (request.getKeyWords() == null || request.getKeyWords().trim().isEmpty()) {
            log.warn("搜索关键词为空");
            return Result.ok();
        }
        
        if (request.getLimit() == null || request.getLimit() <= 0) {
            request.setLimit(10); // 设置默认值
        }
        
        try {
            ComprehensiveSearchVo result = searchLogService.comprehensiveSearch(
                    request.getKeyWords().trim(),
                    request.getPageType(), 
                    request.getLimit());
            
            log.info("综合搜索完成，关键词：{}", request.getKeyWords());
            return Result.ok(result.getData());
            
        } catch (Exception e) {
            log.error("综合搜索异常，关键词：{}", request.getKeyWords(), e);
            return Result.error("搜索失败，请稍后重试");
        }
    }

}
