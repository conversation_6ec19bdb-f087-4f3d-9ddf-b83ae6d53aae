package com.hzw.heterogeneous.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 数据同步请求
 * <AUTHOR>
 * @date 2025-08-31
 */
@ApiModel("数据同步请求")
@Data
public class DataSyncReq {

    @ApiModelProperty(value = "源数据库连接配置", required = true)
    @Valid
    @NotNull(message = "源数据库连接配置不能为空")
    private DatabaseConnectionReq sourceDatabase;

    @ApiModelProperty(value = "源表名", required = true)
    @NotBlank(message = "源表名不能为空")
    private String sourceTable;

    @ApiModelProperty(value = "目标数据库名（在当前MySQL中创建）", required = true)
    @NotBlank(message = "目标数据库名不能为空")
    private String targetDatabase;

    @ApiModelProperty(value = "目标表名（在目标数据库中创建）", required = true)
    @NotBlank(message = "目标表名不能为空")
    private String targetTable;

    @ApiModelProperty(value = "是否覆盖已存在的表，默认false")
    private Boolean overwriteTable = false;

    @ApiModelProperty(value = "同步模式：1-全量同步, 2-增量同步, 默认全量同步")
    private Integer syncMode = 1;

    @ApiModelProperty(value = "批量大小，默认1000条")
    private Integer batchSize = 1000;

    @ApiModelProperty(value = "MongoDB特殊配置")
    private MongoSyncConfig mongoConfig;

    @ApiModelProperty(value = "Oracle特殊配置")
    private OracleSyncConfig oracleConfig;

    @ApiModelProperty(value = "同步描述")
    private String description;

    /**
     * MongoDB同步配置
     */
    @Data
    @ApiModel("MongoDB同步配置")
    public static class MongoSyncConfig {
        @ApiModelProperty(value = "集合名（MongoDB中的表）")
        private String collection;

        @ApiModelProperty(value = "查询条件JSON")
        private String queryFilter;

        @ApiModelProperty(value = "存储格式：1-JSON字符串, 2-展开为字段")
        private Integer storageFormat = 1;

        @ApiModelProperty(value = "需要展开的字段列表（当storageFormat=2时使用）")
        private List<String> expandFields;
    }

    /**
     * Oracle同步配置
     */
    @Data
    @ApiModel("Oracle同步配置")
    public static class OracleSyncConfig {
        @ApiModelProperty(value = "Schema名称")
        private String schema;

        @ApiModelProperty(value = "查询条件WHERE子句")
        private String whereClause;

        @ApiModelProperty(value = "字段映射配置")
        private List<FieldMapping> fieldMappings;
    }

    /**
     * 字段映射配置
     */
    @Data
    @ApiModel("字段映射配置")
    public static class FieldMapping {
        @ApiModelProperty(value = "源字段名")
        private String sourceField;

        @ApiModelProperty(value = "目标字段名")
        private String targetField;

        @ApiModelProperty(value = "数据类型转换")
        private String dataTypeMapping;
    }
}