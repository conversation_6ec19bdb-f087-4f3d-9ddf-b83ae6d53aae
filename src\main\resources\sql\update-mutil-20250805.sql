

ALTER TABLE `t_news` ADD FULLTEXT INDEX `idx_fulltext_title_content` (`title`, `content`) WITH PARSER `ngram`;
-- 修改t_events表的全文索引
ALTER TABLE `t_events` ADD FULLTEXT INDEX `idx_full`(`title`, `content`, `tags`) WITH PARSER `ngram`;

-- 修改t_literature表的全文索引
ALTER TABLE `t_literature` ADD FULLTEXT INDEX `idx_full`(`title`, `summary`) WITH PARSER `ngram`;

-- 修改t_person表的全文索引
ALTER TABLE `t_person` ADD FULLTEXT INDEX `idx_full`(`user_name`, `tags`) WITH PARSER `ngram`;
ALTER TABLE `t_person`
    ADD FULLTEXT INDEX `idx_tags`(`tags`) WITH PARSER `ngram`;
-- 修改t_picture表的全文索引
ALTER TABLE `t_picture` ADD FULLTEXT INDEX `idx_full`(`name`, `tags`) WITH PARSER `ngram`;

-- 修改t_video表的全文索引
ALTER TABLE `t_video` ADD FULLTEXT INDEX `idx_full`(`name`, `tags`) WITH PARSER `ngram`;

-- 修改t_weapon表的全文索引
ALTER TABLE `t_weapon` ADD FULLTEXT INDEX `idx_full`(`name`, `tags`) WITH PARSER `ngram`;
ALTER TABLE `t_weapon`
    ADD FULLTEXT INDEX `idx_tags`(`tags`) WITH PARSER `ngram`;