package com.hzw.heterogeneous.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzw.heterogeneous.controller.response.HotNewsVo;
import com.hzw.heterogeneous.controller.response.NewsVo;
import com.hzw.heterogeneous.model.HotTopics;
import com.hzw.heterogeneous.model.condition.ThematicAnalysisCondition;
import org.apache.ibatis.annotations.Param;

/**
* HotTopicsMapper接口 - 热点话题数据访问层
* 对应数据库表: t_hot_topics
*/
public interface HotTopicsMapper extends BaseMapper<HotTopics> {

    /**
     * 分页查询热点话题列表
     * @param page 分页对象
     * @param condition 查询条件
     * @return 分页结果
     */
    IPage<HotNewsVo> listPage(@Param("page") IPage<HotNewsVo> page, @Param("condition")ThematicAnalysisCondition condition);

    /**
     * 根据热点话题ID分页查询关联的新闻列表
     * @param page 分页对象
     * @param hotTopicsId 热点话题ID
     * @return 关联新闻列表
     */
    IPage<NewsVo> listRelatedNewsByHotTopicsId(@Param("page") IPage<NewsVo> page, @Param("hotTopicsId") String hotTopicsId);
}