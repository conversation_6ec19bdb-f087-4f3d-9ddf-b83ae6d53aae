package com.hzw.heterogeneous.controller.response;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @datetime 2024/05/14 14:15
 * @description: 推荐实体vo
 * @version: 1.0
 */
@Data
public class EntitySearchVo implements Serializable {

    @ApiModelProperty(value = "名称")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "详情")
    private String detail;

    @ApiModelProperty(value = "图片url")
    private String url;

    @ApiModelProperty(value = "相关度")
    private String relevance;

    @ApiModelProperty(value = "base64")
    private String base64;
}
